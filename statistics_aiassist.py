# Started by AICoder, pid:2b3c7q3257ze6a5144e80ba1800a7e8fb8073d7f
import pandas as pd


def load_data(file_path):
    # 读取Excel文件
    df = pd.read_excel(file_path)
    return df


def count_op_judge(df):
    # 统计所有数据的数量
    total_count = len(df)

    # 统计各个op_judge的数量
    op_judge_counts = df['op_judge'].value_counts()

    # 计算比例
    op_judge_percentages = (op_judge_counts / total_count) * 100

    # 格式化百分比
    op_judge_percentages_formatted = op_judge_percentages.apply(
        lambda x: f"{x:.2f}%" if x not in [0, 100] else f"{int(x)}%")

    return op_judge_percentages_formatted


def count_bad_type(df):
    # 统计bad_type的数量
    bad_type_counts = df['bad_type'].value_counts()

    # 按照数量降序排列
    bad_type_counts_sorted = bad_type_counts.sort_values(ascending=False)

    # 统计各个op_judge中各种类型的数量
    bad_type_op_judge_counts = df.groupby(['bad_type', 'op_judge']).size().unstack(fill_value=0)

    # 计算比例
    bad_type_op_judge_percentages = bad_type_op_judge_counts.div(bad_type_counts_sorted, axis=0) * 100

    # 格式化百分比
    bad_type_op_judge_percentages_formatted = bad_type_op_judge_percentages.applymap(
        lambda x: f"{x:.2f}%" if x not in [0, 100] else f"{int(x)}%"
    )

    return bad_type_counts_sorted, bad_type_op_judge_percentages_formatted


def count_materialcode_bad_type(df):
    # 统计materialcode-bad_type的数量
    materialcode_bad_type_counts = df.groupby(['materialcode', 'bad_type']).size().unstack(fill_value=0).stack()

    # 按照数量降序排列
    materialcode_bad_type_counts_sorted = materialcode_bad_type_counts.sort_values(ascending=False)

    # 统计各个op_judge中各种类型的数量
    materialcode_bad_type_op_judge_counts = df.groupby(['materialcode', 'bad_type', 'op_judge']).size().unstack(
        fill_value=0)

    # 计算比例
    materialcode_bad_type_op_judge_percentages = materialcode_bad_type_op_judge_counts.div(
        materialcode_bad_type_counts_sorted, axis=0) * 100

    # 格式化百分比
    materialcode_bad_type_op_judge_percentages_formatted = materialcode_bad_type_op_judge_percentages.applymap(
        lambda x: f"{x:.2f}%" if x not in [0, 100] else f"{int(x)}%"
    )

    return materialcode_bad_type_counts_sorted, materialcode_bad_type_op_judge_percentages_formatted


def main():
    file_path = r"D:\proj\22Manus\02data\14aoi_statistics\毫米波NG判定数据.xlsx"
    file_path = r"D:\proj\22Manus\02data\14aoi_statistics\data\0626\毫米波NG判定数据(0505~0619).xlsx"
    file_path = r"D:\proj\22Manus\02data\14aoi_statistics\data\0807\毫米波NG判定数据(7727903).xlsx"
    # file_path = r"D:\proj\27Augment\07AOI\aoi_mds_data\xlsxs\aoi_data_2025-08-05.xlsx"
    output_file = r"D:\proj\22Manus\02data\14aoi_statistics\毫米波NG判定数据统计.xlsx"
    df = load_data(file_path)

    # 统计op_judge
    op_judge_percentages = count_op_judge(df)
    print("Op Judge Percentages:")
    print(op_judge_percentages)

    # 统计bad_type
    bad_type_counts, bad_type_op_judge_percentages = count_bad_type(df)
    print("\nBad Type Counts:")
    print(bad_type_counts)
    print("\nBad Type Op Judge Percentages:")
    print(bad_type_op_judge_percentages)

    # 统计materialcode-bad_type
    materialcode_bad_type_counts, materialcode_bad_type_op_judge_percentages = count_materialcode_bad_type(df)
    print("\nMaterial Code - Bad Type Counts:")
    print(materialcode_bad_type_counts)
    print("\nMaterial Code - Bad Type Op Judge Percentages:")
    print(materialcode_bad_type_op_judge_percentages)


if __name__ == "__main__":
    main()
# Ended by AICoder, pid:2b3c7q3257ze6a5144e80ba1800a7e8fb8073d7f