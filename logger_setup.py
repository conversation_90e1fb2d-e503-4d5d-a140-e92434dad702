"""
日志系统设置
支持按天滚动日志，保留指定天数
"""

import logging
import logging.handlers
import os
from datetime import datetime, timedelta
from typing import Dict, Any
import glob


class LoggerSetup:
    """日志系统设置类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化日志系统
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logs_dir = config.get('paths', {}).get('logs_dir', 'logs')
        self.log_level = config.get('logging', {}).get('level', 'INFO')
        self.max_days = config.get('logging', {}).get('max_days', 365)
        self.log_format = config.get('logging', {}).get('format', 
                                   '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        # 确保日志目录存在
        if not os.path.exists(self.logs_dir):
            os.makedirs(self.logs_dir)
        
        self.setup_logging()
        self.cleanup_old_logs()
    
    def setup_logging(self):
        """设置日志系统"""
        # 获取根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.log_level.upper()))
        
        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 创建格式器
        formatter = logging.Formatter(self.log_format)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, self.log_level.upper()))
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # 创建文件处理器（按天滚动）
        log_file = os.path.join(self.logs_dir, 'aoi_data_downloader.log')
        file_handler = logging.handlers.TimedRotatingFileHandler(
            filename=log_file,
            when='midnight',
            interval=1,
            backupCount=self.max_days,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, self.log_level.upper()))
        file_handler.setFormatter(formatter)
        
        # 设置日志文件名格式
        file_handler.suffix = "%Y-%m-%d"
        file_handler.extMatch = r"^\d{4}-\d{2}-\d{2}$"
        
        root_logger.addHandler(file_handler)
        
        # 记录日志系统启动信息
        logger = logging.getLogger(__name__)
        logger.info("日志系统初始化完成")
        logger.info(f"日志目录: {self.logs_dir}")
        logger.info(f"日志级别: {self.log_level}")
        logger.info(f"保留天数: {self.max_days}")
    
    def cleanup_old_logs(self):
        """清理过期的日志文件"""
        try:
            # 计算过期日期
            cutoff_date = datetime.now() - timedelta(days=self.max_days)
            
            # 查找所有日志文件
            log_pattern = os.path.join(self.logs_dir, 'aoi_data_downloader.log.*')
            log_files = glob.glob(log_pattern)
            
            deleted_count = 0
            for log_file in log_files:
                try:
                    # 从文件名提取日期
                    filename = os.path.basename(log_file)
                    date_part = filename.split('.')[-1]
                    
                    # 尝试解析日期
                    file_date = datetime.strptime(date_part, "%Y-%m-%d")
                    
                    # 如果文件过期，删除它
                    if file_date < cutoff_date:
                        os.remove(log_file)
                        deleted_count += 1
                        
                except (ValueError, OSError) as e:
                    # 如果无法解析日期或删除文件，记录警告但继续
                    logger = logging.getLogger(__name__)
                    logger.warning(f"无法处理日志文件 {log_file}: {e}")
            
            if deleted_count > 0:
                logger = logging.getLogger(__name__)
                logger.info(f"清理了 {deleted_count} 个过期日志文件")
                
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"清理日志文件时出错: {e}")
    
    def get_logger(self, name: str = None) -> logging.Logger:
        """
        获取日志器
        
        Args:
            name: 日志器名称，如果为None则使用调用者的模块名
            
        Returns:
            日志器实例
        """
        return logging.getLogger(name)
    
    def log_system_info(self):
        """记录系统信息"""
        logger = logging.getLogger(__name__)
        logger.info("=" * 50)
        logger.info("AOI数据下载器启动")
        logger.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"日志目录: {self.logs_dir}")
        logger.info(f"日志级别: {self.log_level}")
        logger.info("=" * 50)


class LoggerMixin:
    """日志混入类，为其他类提供日志功能"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志器"""
        return logging.getLogger(self.__class__.__name__)


if __name__ == "__main__":
    """测试日志系统"""
    import sys
    
    # 测试配置
    test_config = {
        'paths': {
            'logs_dir': 'test_logs'
        },
        'logging': {
            'level': 'INFO',
            'max_days': 7,
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        }
    }
    
    try:
        # 初始化日志系统
        logger_setup = LoggerSetup(test_config)
        
        # 获取日志器并测试
        logger = logger_setup.get_logger(__name__)
        
        logger.info("这是一条信息日志")
        logger.warning("这是一条警告日志")
        logger.error("这是一条错误日志")
        
        # 记录系统信息
        logger_setup.log_system_info()
        
        print("✓ 日志系统测试成功")
        
        # 清理测试目录
        import shutil
        if os.path.exists('test_logs'):
            shutil.rmtree('test_logs')
            print("✓ 清理测试文件成功")
            
    except Exception as e:
        print(f"✗ 日志系统测试失败: {e}")
        sys.exit(1)
