"""
数据下载管理器
基于AOIAPIClient实现分页数据下载、时间过滤、数据存储等核心功能
"""

import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Callable
import threading
from logger_setup import LoggerMixin
from aoi_api_client import AOIAPIClient
from excel_data_manager import DataFileManager
from config_manager import ConfigManager


class DataDownloadManager(LoggerMixin):
    """数据下载管理器"""
    
    def __init__(self, config: Dict):
        """
        初始化数据下载管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.api_client = AOIAPIClient(config)

        # 获取数据目录和文件格式
        data_dir = config.get('paths', {}).get('data_dir', 'xlsxs')
        file_format = config.get('download', {}).get('file_format', 'csv')
        self.data_manager = DataFileManager(data_dir, file_format)
        self.config_manager = ConfigManager()
        
        # 下载控制
        self.is_downloading = False
        self.should_stop = False
        self.download_thread = None
        
        # 统计信息
        self.stats = {
            'total_pages': 0,
            'total_records': 0,
            'saved_records': 0,
            'empty_pages': 0,
            'start_time': None,
            'end_time': None,
            'last_create_time': None
        }
        
        # 回调函数
        self.progress_callback = None
        self.status_callback = None
    
    def set_progress_callback(self, callback: Callable[[Dict], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def set_status_callback(self, callback: Callable[[str], None]):
        """设置状态回调函数"""
        self.status_callback = callback
    
    def _notify_progress(self, progress_info: Dict):
        """通知进度更新"""
        if self.progress_callback:
            self.progress_callback(progress_info)
    
    def _notify_status(self, status: str):
        """通知状态更新"""
        self.logger.info(status)
        if self.status_callback:
            self.status_callback(status)
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_pages': 0,
            'total_records': 0,
            'saved_records': 0,
            'empty_pages': 0,
            'start_time': None,
            'end_time': None,
            'last_create_time': None
        }
    
    def should_stop_download(self, create_time: str, start_time: str) -> bool:
        """
        判断是否应该停止下载（到达开始时间）

        Args:
            create_time: 当前数据的创建时间
            start_time: 开始下载时间

        Returns:
            是否应该停止下载
        """
        try:
            if not create_time or not start_time:
                return False

            # 解析时间
            current_dt = datetime.strptime(create_time, "%Y-%m-%d %H:%M:%S")
            start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")

            # 如果当前时间早于或等于开始时间，停止下载
            return current_dt <= start_dt

        except ValueError as e:
            self.logger.error(f"时间格式错误: {e}")
            return False

    def should_skip_data(self, create_time: str, start_time: str, stop_time: str) -> bool:
        """
        判断是否应该跳过当前数据（不在时间范围内）

        Args:
            create_time: 当前数据的创建时间
            start_time: 开始下载时间
            stop_time: 停止下载时间

        Returns:
            是否应该跳过数据
        """
        try:
            if not create_time:
                return True

            current_dt = datetime.strptime(create_time, "%Y-%m-%d %H:%M:%S")

            if start_time:
                start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                if current_dt <= start_dt:
                    return True

            if stop_time:
                stop_dt = datetime.strptime(stop_time, "%Y-%m-%d %H:%M:%S")
                if current_dt > stop_dt:
                    return True

            return False

        except ValueError as e:
            self.logger.error(f"时间格式错误: {e}")
            return True
    
    def download_data(self, start_time: str, stop_time: str = None) -> bool:
        """
        下载数据的主要方法

        Args:
            start_time: 开始下载时间，格式: YYYY-MM-DD HH:MM:SS
            stop_time: 停止下载时间，格式: YYYY-MM-DD HH:MM:SS（可选）

        Returns:
            是否下载成功
        """
        try:
            self.reset_stats()
            self.stats['start_time'] = datetime.now()
            self.is_downloading = True
            self.should_stop = False
            
            if stop_time:
                self._notify_status(f"开始下载数据，时间范围: {start_time} 到 {stop_time}")
            else:
                self._notify_status(f"开始下载数据，开始时间: {start_time}")
            
            page_num = 0
            empty_page_count = 0
            max_empty_pages = self.config.get('download', {}).get('max_empty_pages', 5)
            jump_step = self.config.get('download', {}).get('jump_step', 5)
            is_jumping = False
            last_jump_page = 0
            
            while not self.should_stop:
                self._notify_status(f"正在下载第 {page_num} 页数据...")
                
                # 获取数据
                success, data = self.api_client.fetch_data(page_num)
                
                if not success:
                    self._notify_status(f"第 {page_num} 页数据获取失败")
                    break
                
                rows = data.get('rows', [])
                self.stats['total_pages'] += 1
                
                if not rows:
                    # 空页处理
                    empty_page_count += 1
                    self.stats['empty_pages'] += 1
                    self._notify_status(f"第 {page_num} 页没有数据，连续空页: {empty_page_count}")
                    
                    if empty_page_count >= max_empty_pages:
                        self._notify_status(f"连续 {max_empty_pages} 页没有数据，停止下载")
                        break
                    
                    page_num += 1
                    continue
                
                # 重置空页计数
                empty_page_count = 0
                
                # 获取时间范围
                min_time, max_time = self.api_client.get_time_range_from_data(rows)
                self.stats['total_records'] += len(rows)
                
                if min_time:
                    self.stats['last_create_time'] = max_time
                
                self._notify_status(f"第 {page_num} 页获取 {len(rows)} 条数据，时间范围: {min_time} ~ {max_time}")

                # 检查是否应该停止下载（数据时间已达到开始时间）
                if min_time and self.should_stop_download(min_time, start_time):
                    self._notify_status(f"第 {page_num} 页数据时间 {min_time} 已达到开始时间 {start_time}，停止下载")

                    # 过滤出在时间范围内的数据
                    filtered_rows = []
                    for row in rows:
                        row_time = row.get('createTime')
                        if row_time and not self.should_skip_data(row_time, start_time, stop_time):
                            filtered_rows.append(row)

                    if filtered_rows:
                        success, summary = self.data_manager.save_grouped_data(filtered_rows)
                        if success:
                            saved_count = sum(summary.values())
                            self.stats['saved_records'] += saved_count
                            save_details = [f"{date_str}: {count}条" for date_str, count in summary.items()]
                            self._notify_status(f"最后一页保存 {saved_count} 条数据: {', '.join(save_details)}")

                    break

                # 检查是否需要跳跃下载
                if stop_time and max_time:
                    try:
                        max_dt = datetime.strptime(max_time, "%Y-%m-%d %H:%M:%S")
                        stop_dt = datetime.strptime(stop_time, "%Y-%m-%d %H:%M:%S")

                        if max_dt > stop_dt:
                            # 数据时间晚于停止时间
                            if not is_jumping and page_num == 0:
                                # 第一页就超出范围，开始跳跃
                                is_jumping = True
                                last_jump_page = page_num
                                page_num += jump_step
                                self._notify_status(f"第 {last_jump_page} 页数据时间 {max_time} 晚于停止时间 {stop_time}，跳跃到第 {page_num} 页")
                                continue
                            elif is_jumping:
                                # 继续跳跃
                                last_jump_page = page_num
                                page_num += jump_step
                                self._notify_status(f"第 {last_jump_page} 页数据仍晚于停止时间，跳跃到第 {page_num} 页")
                                continue
                            else:
                                # 正常模式下跳过
                                page_num += 1
                                self._notify_status(f"第 {page_num-1} 页数据时间 {max_time} 晚于停止时间 {stop_time}，跳过")
                                continue
                        elif is_jumping:
                            # 跳跃模式下找到合适的时间范围，回退到上次跳跃位置
                            self._notify_status(f"第 {page_num} 页数据时间 {max_time} 符合范围，回退到第 {last_jump_page + 1} 页开始逐页下载")
                            page_num = last_jump_page + 1
                            is_jumping = False
                            continue
                    except ValueError:
                        pass

                # 过滤数据：只保存在时间范围内的数据
                filtered_rows = []
                for row in rows:
                    row_time = row.get('createTime')
                    if row_time and not self.should_skip_data(row_time, start_time, stop_time):
                        filtered_rows.append(row)

                # 保存数据
                if filtered_rows:
                    success, summary = self.data_manager.save_grouped_data(filtered_rows)
                    if success:
                        saved_count = sum(summary.values())
                        self.stats['saved_records'] += saved_count

                        # 构建保存详情
                        save_details = []
                        for date_str, count in summary.items():
                            save_details.append(f"{date_str}: {count}条")

                        self._notify_status(f"第 {page_num} 页保存 {saved_count} 条数据: {', '.join(save_details)}")
                    else:
                        self._notify_status(f"第 {page_num} 页数据保存失败")
                else:
                    self._notify_status(f"第 {page_num} 页没有符合时间范围的数据")
                
                # 更新进度
                progress_info = {
                    'page_num': page_num,
                    'total_pages': self.stats['total_pages'],
                    'total_records': self.stats['total_records'],
                    'saved_records': self.stats['saved_records'],
                    'time_range': f"{min_time} ~ {max_time}" if min_time else "无数据"
                }
                self._notify_progress(progress_info)
                
                page_num += 1
                
                # 短暂延迟，避免请求过于频繁
                time.sleep(0.1)
            
            # 下载完成
            self.stats['end_time'] = datetime.now()
            duration = self.stats['end_time'] - self.stats['start_time']
            
            if self.should_stop:
                self._notify_status("下载已被用户停止")
            else:
                self._notify_status(f"下载完成！总计: {self.stats['total_pages']} 页，"
                                  f"{self.stats['total_records']} 条记录，"
                                  f"保存 {self.stats['saved_records']} 条，"
                                  f"耗时: {duration}")
                
                # 更新最后更新时间
                if self.stats['last_create_time']:
                    self.config_manager.update_last_update_time(self.stats['last_create_time'])
                    self._notify_status(f"更新最后更新时间: {self.stats['last_create_time']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"下载数据时出错: {e}")
            self._notify_status(f"下载出错: {e}")
            return False
        finally:
            self.is_downloading = False
    
    def start_download_async(self, start_time: str, stop_time: str = None):
        """
        异步开始下载

        Args:
            start_time: 开始下载时间
            stop_time: 停止下载时间（可选）
        """
        if self.is_downloading:
            self._notify_status("下载已在进行中")
            return

        self.download_thread = threading.Thread(
            target=self.download_data,
            args=(start_time, stop_time),
            daemon=True
        )
        self.download_thread.start()
    
    def stop_download(self):
        """停止下载"""
        if self.is_downloading:
            self.should_stop = True
            self._notify_status("正在停止下载...")
        else:
            self._notify_status("当前没有下载任务在运行")
    
    def get_download_status(self) -> Dict:
        """
        获取下载状态
        
        Returns:
            下载状态字典
        """
        return {
            'is_downloading': self.is_downloading,
            'should_stop': self.should_stop,
            'stats': self.stats.copy()
        }


if __name__ == "__main__":
    """测试数据下载管理器"""
    import sys
    from logger_setup import LoggerSetup
    
    try:
        # 加载配置
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        # 初始化日志
        logger_setup = LoggerSetup(config)
        
        # 创建下载管理器
        download_manager = DataDownloadManager(config)
        
        # 设置回调函数
        def progress_callback(progress_info):
            print(f"进度更新: 第{progress_info['page_num']}页，"
                  f"总记录: {progress_info['total_records']}，"
                  f"已保存: {progress_info['saved_records']}")
        
        def status_callback(status):
            print(f"状态: {status}")
        
        download_manager.set_progress_callback(progress_callback)
        download_manager.set_status_callback(status_callback)
        
        # 测试下载（只下载1页）
        print("开始测试下载...")
        start_time = "2025-08-01 00:00:00"
        
        # 同步下载测试
        success = download_manager.download_data(start_time)
        
        if success:
            print("✓ 数据下载管理器测试成功")
            status = download_manager.get_download_status()
            print(f"下载统计: {status['stats']}")
        else:
            print("✗ 数据下载管理器测试失败")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
