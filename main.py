"""
AOI数据下载器增强版主程序
整合所有新功能，提供完整的数据下载和管理功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'PySide6',
        'pandas', 
        'openpyxl',
        'requests',
        'yaml'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'yaml':
                import yaml
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print("pip install PySide6 pandas openpyxl requests PyYAML")
        return False
    
    return True

def create_directories():
    """创建必要的目录"""
    directories = ['data', 'logs', 'configs', 'exports']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")

def main():
    """主函数"""
    print("AOI数据下载器增强版启动中...")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    try:
        from main_window import main as gui_main
        gui_main()
        
    except ImportError as e:
        print(f"导入GUI模块失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
