"""
AOI数据下载器增强版主界面
支持CSV/XLSX格式选择、文件夹路径设置、文件管理、停止时间等新功能
"""

import sys
import os
from datetime import datetime
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                               QWidget, QLabel, QPushButton, QTextEdit, QDateTimeEdit,
                               QGroupBox, QProgressBar, QMessageBox, QSplitter, QTabWidget,
                               QComboBox, QTableWidget, QTableWidgetItem, QDateEdit,
                               QSpinBox, QHeaderView, QFileDialog, QCheckBox)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QDateTime, QDate
from PySide6.QtGui import QFont
from typing import Dict

from config_manager import ConfigManager
from logger_setup import LoggerSetup
from data_download_manager import DataDownloadManager
from aoi_data_statistics import AOIDataStatistics
from excel_data_manager import DataFileManager
from image_downloader import ImageDownloader


class DownloadWorker(QThread):
    """下载工作线程"""
    
    progress_updated = Signal(dict)
    status_updated = Signal(str)
    download_finished = Signal(bool)
    
    def __init__(self, download_manager: DataDownloadManager, start_time: str, stop_time: str = None):
        super().__init__()
        self.download_manager = download_manager
        self.start_time = start_time
        self.stop_time = stop_time
        
        # 设置回调函数
        self.download_manager.set_progress_callback(self.on_progress_update)
        self.download_manager.set_status_callback(self.on_status_update)
    
    def on_progress_update(self, progress_info: Dict):
        """进度更新回调"""
        self.progress_updated.emit(progress_info)
    
    def on_status_update(self, status: str):
        """状态更新回调"""
        self.status_updated.emit(status)
    
    def run(self):
        """运行下载任务"""
        try:
            success = self.download_manager.download_data(self.start_time, self.stop_time)
            self.download_finished.emit(success)
        except Exception as e:
            self.status_updated.emit(f"下载出错: {e}")
            self.download_finished.emit(False)


class StatisticsWorker(QThread):
    """统计分析工作线程"""
    
    status_updated = Signal(str)
    statistics_finished = Signal(dict)
    
    def __init__(self, statistics_manager: AOIDataStatistics, 
                 start_date: str, end_date: str, top_n: int):
        super().__init__()
        self.statistics_manager = statistics_manager
        self.start_date = start_date
        self.end_date = end_date
        self.top_n = top_n
    
    def run(self):
        """运行统计分析任务"""
        try:
            self.status_updated.emit("正在生成统计报告...")
            report = self.statistics_manager.generate_comprehensive_report(
                self.start_date, self.end_date, self.top_n
            )
            self.statistics_finished.emit(report)
        except Exception as e:
            self.status_updated.emit(f"统计分析出错: {e}")
            self.statistics_finished.emit({'error': str(e)})


class MainWindowEnhanced(QMainWindow):
    """增强版主窗口类"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化组件
        self.config_manager = ConfigManager()
        # 强制设置为XLSX格式
        self.config_manager.update_file_format('xlsx')
        self.config = self.config_manager.get_config()
        self.logger_setup = LoggerSetup(self.config)
        self.download_manager = DataDownloadManager(self.config)
        self.statistics_manager = AOIDataStatistics(
            self.config_manager.get_data_dir()
        )
        
        # 工作线程
        self.download_worker = None
        self.statistics_worker = None
        
        # 初始化界面
        self.init_ui()
        
        # 定时器用于更新界面
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_display)
        self.timer.start(1000)  # 每秒更新一次
        
        # 加载初始数据
        self.load_initial_data()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("AOI数据下载器与统计分析 - 增强版")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建下载标签页
        download_tab = self.create_download_tab()
        self.tab_widget.addTab(download_tab, "数据下载")
        
        # 创建文件管理标签页
        file_management_tab = self.create_file_management_tab()
        self.tab_widget.addTab(file_management_tab, "文件管理")
        
        # 创建统计标签页
        statistics_tab = self.create_statistics_tab()
        self.tab_widget.addTab(statistics_tab, "数据统计")
        
        # 创建设置标签页
        settings_tab = self.create_settings_tab()
        self.tab_widget.addTab(settings_tab, "系统设置")
    
    def create_download_tab(self) -> QWidget:
        """创建下载标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # 创建控制面板
        control_panel = self.create_download_control_panel()
        splitter.addWidget(control_panel)
        
        # 创建信息显示面板
        info_panel = self.create_download_info_panel()
        splitter.addWidget(info_panel)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 0)  # 控制面板固定大小
        splitter.setStretchFactor(1, 1)  # 信息面板可伸缩
        
        return tab
    
    def create_download_control_panel(self) -> QWidget:
        """创建下载控制面板"""
        panel = QGroupBox("下载控制")
        layout = QVBoxLayout(panel)
        
        # 文件格式显示（固定为XLSX）
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("文件格式: XLSX（固定）"))
        format_layout.addStretch()
        layout.addLayout(format_layout)
        
        # 时间设置区域
        time_layout = QHBoxLayout()
        
        # 上次更新时间显示
        time_layout.addWidget(QLabel("上次更新到:"))
        self.last_update_label = QLabel("加载中...")
        self.last_update_label.setStyleSheet("color: blue; font-weight: bold;")
        time_layout.addWidget(self.last_update_label)
        
        time_layout.addStretch()
        layout.addLayout(time_layout)
        
        # 下载时间范围设置
        time_range_layout = QHBoxLayout()
        
        time_range_layout.addWidget(QLabel("开始时间:"))
        self.start_time_edit = QDateTimeEdit()
        self.start_time_edit.setDisplayFormat("yyyy-MM-dd hh:mm:ss")
        self.start_time_edit.setCalendarPopup(True)
        time_range_layout.addWidget(self.start_time_edit)
        
        time_range_layout.addWidget(QLabel("停止时间:"))
        self.stop_time_edit = QDateTimeEdit()
        self.stop_time_edit.setDisplayFormat("yyyy-MM-dd hh:mm:ss")
        self.stop_time_edit.setCalendarPopup(True)
        time_range_layout.addWidget(self.stop_time_edit)
        
        # 启用停止时间复选框
        self.enable_stop_time = QCheckBox("启用停止时间")
        self.enable_stop_time.toggled.connect(self.on_stop_time_toggled)
        time_range_layout.addWidget(self.enable_stop_time)

        time_range_layout.addStretch()
        layout.addLayout(time_range_layout)

        # 跳跃下载设置
        jump_layout = QHBoxLayout()
        jump_layout.addWidget(QLabel("跳跃步长:"))
        self.jump_step_spin = QSpinBox()
        self.jump_step_spin.setRange(1, 50)
        self.jump_step_spin.setValue(self.config_manager.get_jump_step())
        self.jump_step_spin.setToolTip("当数据时间超出范围时，跳跃的页数")
        self.jump_step_spin.valueChanged.connect(self.on_jump_step_changed)
        jump_layout.addWidget(self.jump_step_spin)
        jump_layout.addStretch()

        layout.addLayout(jump_layout)

        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始下载")
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }")
        self.start_button.clicked.connect(self.start_download)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止下载")
        self.stop_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 8px; }")
        self.stop_button.clicked.connect(self.stop_download)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        button_layout.addStretch()
        
        # 刷新按钮
        refresh_button = QPushButton("刷新状态")
        refresh_button.clicked.connect(self.refresh_status)
        button_layout.addWidget(refresh_button)
        
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: green; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        return panel
    
    def create_download_info_panel(self) -> QWidget:
        """创建下载信息显示面板"""
        panel = QGroupBox("运行信息")
        layout = QVBoxLayout(panel)
        
        # 统计信息区域
        stats_layout = QHBoxLayout()
        
        self.pages_label = QLabel("页数: 0")
        stats_layout.addWidget(self.pages_label)
        
        self.records_label = QLabel("记录数: 0")
        stats_layout.addWidget(self.records_label)
        
        self.saved_label = QLabel("已保存: 0")
        stats_layout.addWidget(self.saved_label)
        
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        return panel

    def create_file_management_tab(self) -> QWidget:
        """创建文件管理标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 文件列表控制区域
        control_layout = QHBoxLayout()

        # 数据目录显示和修改
        control_layout.addWidget(QLabel("数据目录:"))
        self.data_dir_label = QLabel(self.config_manager.get_data_dir())
        self.data_dir_label.setStyleSheet("border: 1px solid gray; padding: 4px; background: #f0f0f0;")
        control_layout.addWidget(self.data_dir_label)

        change_dir_button = QPushButton("更改目录")
        change_dir_button.clicked.connect(self.change_data_directory)
        control_layout.addWidget(change_dir_button)

        control_layout.addStretch()

        refresh_files_button = QPushButton("刷新文件列表")
        refresh_files_button.clicked.connect(self.refresh_file_list)
        control_layout.addWidget(refresh_files_button)

        check_files_button = QPushButton("检查选中文件")
        check_files_button.clicked.connect(self.check_selected_files)
        control_layout.addWidget(check_files_button)

        layout.addLayout(control_layout)

        # 文件列表表格
        self.file_table = QTableWidget()
        self.file_table.setColumnCount(7)
        self.file_table.setHorizontalHeaderLabels(["文件名", "日期", "格式", "行数", "文件大小", "修改时间", "状态"])
        self.file_table.setAlternatingRowColors(True)
        self.file_table.setSelectionBehavior(QTableWidget.SelectRows)

        # 调整列宽
        header = self.file_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)

        layout.addWidget(self.file_table)

        # 文件操作按钮
        file_ops_layout = QHBoxLayout()

        delete_file_button = QPushButton("删除选中文件")
        delete_file_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        delete_file_button.clicked.connect(self.delete_selected_files)
        file_ops_layout.addWidget(delete_file_button)

        # 清理损坏文件按钮
        cleanup_button = QPushButton("清理损坏文件")
        cleanup_button.setStyleSheet("QPushButton { background-color: #ff9800; color: white; }")
        cleanup_button.clicked.connect(self.cleanup_corrupted_files)
        file_ops_layout.addWidget(cleanup_button)

        file_ops_layout.addStretch()

        layout.addLayout(file_ops_layout)

        return tab

    def create_settings_tab(self) -> QWidget:
        """创建设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 路径设置组
        path_group = QGroupBox("路径设置")
        path_layout = QVBoxLayout(path_group)

        # 数据目录设置
        data_dir_layout = QHBoxLayout()
        data_dir_layout.addWidget(QLabel("数据目录:"))
        self.settings_data_dir_label = QLabel(self.config_manager.get_data_dir())
        self.settings_data_dir_label.setStyleSheet("border: 1px solid gray; padding: 4px; background: #f0f0f0;")
        data_dir_layout.addWidget(self.settings_data_dir_label)

        change_data_dir_button = QPushButton("更改")
        change_data_dir_button.clicked.connect(self.change_data_directory)
        data_dir_layout.addWidget(change_data_dir_button)

        path_layout.addLayout(data_dir_layout)

        # 导出目录设置
        export_dir_layout = QHBoxLayout()
        export_dir_layout.addWidget(QLabel("导出目录:"))
        self.export_dir_label = QLabel(self.config_manager.get_export_dir())
        self.export_dir_label.setStyleSheet("border: 1px solid gray; padding: 4px; background: #f0f0f0;")
        export_dir_layout.addWidget(self.export_dir_label)

        change_export_dir_button = QPushButton("更改")
        change_export_dir_button.clicked.connect(self.change_export_directory)
        export_dir_layout.addWidget(change_export_dir_button)

        path_layout.addLayout(export_dir_layout)

        layout.addWidget(path_group)

        # 下载设置组
        download_group = QGroupBox("下载设置")
        download_layout = QVBoxLayout(download_group)

        # 文件格式显示（固定为CSV）
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("文件格式: CSV（固定）"))
        format_layout.addStretch()

        download_layout.addLayout(format_layout)

        layout.addWidget(download_group)

        layout.addStretch()

        return tab

    def create_statistics_tab(self) -> QWidget:
        """创建统计标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建统计控制面板
        stats_control_panel = self.create_statistics_control_panel()
        layout.addWidget(stats_control_panel)

        # 创建统计结果显示面板
        stats_result_panel = self.create_statistics_result_panel()
        layout.addWidget(stats_result_panel)

        return tab

    def create_statistics_control_panel(self) -> QWidget:
        """创建统计控制面板"""
        panel = QGroupBox("统计参数设置")
        layout = QVBoxLayout(panel)

        # 时间范围设置
        time_layout = QHBoxLayout()

        time_layout.addWidget(QLabel("开始日期:"))
        self.stats_start_date = QDateEdit()
        self.stats_start_date.setDisplayFormat("yyyy-MM-dd")
        self.stats_start_date.setCalendarPopup(True)
        # 从配置文件读取
        start_date_str = self.config_manager.get_stats_start_date()
        start_date = QDate.fromString(start_date_str, "yyyy-MM-dd")
        self.stats_start_date.setDate(start_date)
        self.stats_start_date.dateChanged.connect(self.on_stats_params_changed)
        time_layout.addWidget(self.stats_start_date)

        time_layout.addWidget(QLabel("结束日期:"))
        self.stats_end_date = QDateEdit()
        self.stats_end_date.setDisplayFormat("yyyy-MM-dd")
        self.stats_end_date.setCalendarPopup(True)
        # 从配置文件读取
        end_date_str = self.config_manager.get_stats_end_date()
        end_date = QDate.fromString(end_date_str, "yyyy-MM-dd")
        self.stats_end_date.setDate(end_date)
        self.stats_end_date.dateChanged.connect(self.on_stats_params_changed)
        time_layout.addWidget(self.stats_end_date)

        time_layout.addStretch()

        # Top N设置
        time_layout.addWidget(QLabel("Top N:"))
        self.stats_top_n = QSpinBox()
        self.stats_top_n.setRange(5, 50)
        # 从配置文件读取
        self.stats_top_n.setValue(self.config_manager.get_stats_top_n())
        self.stats_top_n.valueChanged.connect(self.on_stats_params_changed)
        time_layout.addWidget(self.stats_top_n)

        layout.addLayout(time_layout)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.stats_generate_button = QPushButton("生成统计报告")
        self.stats_generate_button.setStyleSheet(
            "QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 8px; }"
        )
        self.stats_generate_button.clicked.connect(self.generate_statistics)
        button_layout.addWidget(self.stats_generate_button)

        self.stats_stop_button = QPushButton("停止生成")
        self.stats_stop_button.setStyleSheet(
            "QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 8px; }"
        )
        self.stats_stop_button.clicked.connect(self.stop_statistics)
        self.stats_stop_button.setEnabled(False)
        button_layout.addWidget(self.stats_stop_button)

        # 下载图片按钮
        self.download_nj_images_button = QPushButton("下载南京图片")
        self.download_nj_images_button.setStyleSheet(
            "QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }"
        )
        self.download_nj_images_button.setEnabled(False)
        self.download_nj_images_button.clicked.connect(self.download_nanjing_images)
        button_layout.addWidget(self.download_nj_images_button)

        self.download_hy_images_button = QPushButton("下载河源图片")
        self.download_hy_images_button.setStyleSheet(
            "QPushButton { background-color: #9E9E9E; color: white; font-weight: bold; padding: 8px; }"
        )
        self.download_hy_images_button.setEnabled(False)
        self.download_hy_images_button.clicked.connect(self.download_heyuan_images)
        button_layout.addWidget(self.download_hy_images_button)

        self.stats_export_xlsx_button = QPushButton("导出XLSX报告")
        self.stats_export_xlsx_button.setEnabled(False)
        self.stats_export_xlsx_button.clicked.connect(self.export_xlsx_report)
        button_layout.addWidget(self.stats_export_xlsx_button)

        self.stats_export_html_button = QPushButton("导出HTML报告")
        self.stats_export_html_button.setEnabled(False)
        self.stats_export_html_button.clicked.connect(self.export_html_report)
        button_layout.addWidget(self.stats_export_html_button)

        button_layout.addStretch()

        # 状态标签
        self.stats_status_label = QLabel("就绪")
        self.stats_status_label.setStyleSheet("color: green; font-weight: bold;")
        button_layout.addWidget(self.stats_status_label)

        layout.addLayout(button_layout)

        return panel

    def create_statistics_result_panel(self) -> QWidget:
        """创建统计结果显示面板"""
        panel = QGroupBox("统计结果")
        layout = QVBoxLayout(panel)

        # 创建地区选择标签页
        self.region_tab_widget = QTabWidget()
        layout.addWidget(self.region_tab_widget)

        # 为每个地区创建标签页
        self.region_stats = {}
        regions = ['南京', '河源', '其他']

        for region in regions:
            region_widget = QWidget()
            region_layout = QVBoxLayout(region_widget)

            # 为每个地区创建子标签页
            region_sub_tabs = QTabWidget()
            region_layout.addWidget(region_sub_tabs)

            # 总览标签页
            overview_tab = QWidget()
            overview_layout = QVBoxLayout(overview_tab)
            overview_text = QTextEdit()
            overview_text.setReadOnly(True)
            overview_text.setFont(QFont("Consolas", 9))
            overview_layout.addWidget(overview_text)
            region_sub_tabs.addTab(overview_tab, "总览")

            # 各类缺陷统计标签页
            defect_tab = QWidget()
            defect_layout = QVBoxLayout(defect_tab)
            defect_table = QTableWidget()
            defect_table.setAlternatingRowColors(True)
            defect_layout.addWidget(defect_table)
            region_sub_tabs.addTab(defect_tab, "各类缺陷统计")

            # 代码TOP N标签页
            code_tab = QWidget()
            code_layout = QVBoxLayout(code_tab)
            code_table = QTableWidget()
            code_table.setAlternatingRowColors(True)
            code_layout.addWidget(code_table)
            region_sub_tabs.addTab(code_tab, "代码TOP N")

            # 保存引用
            self.region_stats[region] = {
                'overview_text': overview_text,
                'defect_table': defect_table,
                'code_table': code_table
            }

            self.region_tab_widget.addTab(region_widget, region)

        return panel

    def load_initial_data(self):
        """加载初始数据"""
        try:
            # 显示上次下载时间范围
            last_start_time = self.config_manager.get_last_download_start_time()
            last_end_time = self.config_manager.get_last_download_end_time()
            self.last_update_label.setText(f"{last_start_time} ~ {last_end_time}")

            # 设置开始时间为上次下载结束时间加一天
            try:
                from datetime import timedelta
                last_end_dt = datetime.strptime(last_end_time, "%Y-%m-%d %H:%M:%S")
                next_start_dt = last_end_dt + timedelta(days=1)
                next_start_time = next_start_dt.strftime("%Y-%m-%d %H:%M:%S")
                start_dt = QDateTime.fromString(next_start_time, "yyyy-MM-dd hh:mm:ss")
                self.stop_time_edit.setDateTime(start_dt)
            except:
                # 如果解析失败，使用默认时间
                start_dt = QDateTime.fromString("2025-08-01 00:00:00", "yyyy-MM-dd hh:mm:ss")
                self.stop_time_edit.setDateTime(start_dt)

            # 设置停止时间为上次下载开始时间加一天
            try:
                last_start_dt = datetime.strptime(last_start_time, "%Y-%m-%d %H:%M:%S")
                next_end_dt = last_start_dt + timedelta(days=1)
                next_end_time = next_end_dt.strftime("%Y-%m-%d %H:%M:%S")
                stop_dt = QDateTime.fromString(next_end_time, "yyyy-MM-dd hh:mm:ss")
                self.start_time_edit.setDateTime(stop_dt)
            except:
                # 如果解析失败，使用默认时间
                stop_dt = QDateTime.fromString("2025-08-01 23:59:59", "yyyy-MM-dd hh:mm:ss")
                self.start_time_edit.setDateTime(stop_dt)
            self.enable_stop_time.setChecked(True)
            # 刷新文件列表
            self.refresh_file_list()

            self.add_log("系统初始化完成")

        except Exception as e:
            self.add_log(f"加载初始数据失败: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")

    def add_log(self, message: str):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)

        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def on_format_changed(self, format_text: str):
        """文件格式改变事件"""
        try:
            if self.config_manager.update_file_format(format_text):
                self.add_log(f"文件格式已更改为: {format_text}")
                # 同步更新设置页面的格式选择
                self.settings_format_combo.setCurrentText(format_text)
                # 重新初始化下载管理器
                self.download_manager = DataDownloadManager(self.config_manager.get_config())
            else:
                self.add_log(f"更改文件格式失败")
        except Exception as e:
            self.add_log(f"更改文件格式出错: {e}")

    def on_settings_format_changed(self, format_text: str):
        """设置页面文件格式改变事件"""
        try:
            if self.config_manager.update_file_format(format_text):
                self.add_log(f"默认文件格式已更改为: {format_text}")
                # 同步更新下载页面的格式选择
                self.format_combo.setCurrentText(format_text)
                # 重新初始化下载管理器
                self.download_manager = DataDownloadManager(self.config_manager.get_config())
            else:
                self.add_log(f"更改默认文件格式失败")
        except Exception as e:
            self.add_log(f"更改默认文件格式出错: {e}")

    def on_stats_params_changed(self):
        """统计参数变化处理"""
        try:
            start_date = self.stats_start_date.date().toString("yyyy-MM-dd")
            end_date = self.stats_end_date.date().toString("yyyy-MM-dd")
            top_n = self.stats_top_n.value()

            # 更新配置文件
            if self.config_manager.update_stats_params(start_date, end_date, top_n):
                self.add_log(f"统计参数已更新: {start_date} ~ {end_date}, Top {top_n}")
            else:
                self.add_log("统计参数更新失败")

        except Exception as e:
            self.add_log(f"更新统计参数出错: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")

    def on_jump_step_changed(self, value: int):
        """跳跃步长变化处理"""
        try:
            if self.config_manager.update_jump_step(value):
                self.add_log(f"跳跃步长已更新为: {value}")
            else:
                self.add_log("跳跃步长更新失败")
        except Exception as e:
            self.add_log(f"更新跳跃步长出错: {e}")

    def on_stop_time_toggled(self, checked: bool):
        """停止时间启用状态改变事件"""
        self.stop_time_edit.setEnabled(checked)
        if checked:
            self.add_log("已启用停止时间")
        else:
            self.add_log("已禁用停止时间")

    def change_data_directory(self):
        """更改数据目录"""
        try:
            current_dir = self.config_manager.get_data_dir()
            new_dir = QFileDialog.getExistingDirectory(
                self, "选择数据目录", current_dir
            )

            if new_dir and new_dir != current_dir:
                if self.config_manager.update_data_dir(new_dir):
                    self.data_dir_label.setText(new_dir)
                    self.settings_data_dir_label.setText(new_dir)
                    self.add_log(f"数据目录已更改为: {new_dir}")

                    # 重新初始化相关组件
                    self.download_manager = DataDownloadManager(self.config_manager.get_config())
                    self.statistics_manager = AOIDataStatistics(new_dir)

                    # 刷新文件列表
                    self.refresh_file_list()
                else:
                    QMessageBox.warning(self, "错误", "更改数据目录失败")

        except Exception as e:
            self.add_log(f"更改数据目录出错: {e}")
            QMessageBox.critical(self, "错误", f"更改数据目录出错: {e}")

    def change_export_directory(self):
        """更改导出目录"""
        try:
            current_dir = self.config_manager.get_export_dir()
            new_dir = QFileDialog.getExistingDirectory(
                self, "选择导出目录", current_dir
            )

            if new_dir and new_dir != current_dir:
                if self.config_manager.update_export_dir(new_dir):
                    self.export_dir_label.setText(new_dir)
                    self.add_log(f"导出目录已更改为: {new_dir}")
                else:
                    QMessageBox.warning(self, "错误", "更改导出目录失败")

        except Exception as e:
            self.add_log(f"更改导出目录出错: {e}")
            QMessageBox.critical(self, "错误", f"更改导出目录出错: {e}")

    def refresh_file_list(self):
        """刷新文件列表（快速模式，不读取文件内容）"""
        try:
            self.add_log("开始刷新文件列表...")

            # 获取数据目录
            data_dir = self.config_manager.get_data_dir()

            if not os.path.exists(data_dir):
                self.add_log(f"数据目录不存在: {data_dir}")
                self.file_table.setRowCount(0)
                return

            # 快速获取文件列表（不读取文件内容）
            files = []
            try:
                for filename in os.listdir(data_dir):
                    if (filename.endswith('.csv') or filename.endswith('.xlsx')) and filename.startswith('aoi_data_'):
                        file_path = os.path.join(data_dir, filename)
                        files.append(file_path)
            except Exception as e:
                self.add_log(f"读取目录失败: {e}")
                return

            files.sort()

            # 更新表格
            self.file_table.setRowCount(len(files))

            for row, file_path in enumerate(files):
                try:
                    filename = os.path.basename(file_path)
                    file_ext = os.path.splitext(filename)[1].lower()
                    date_part = filename.replace('aoi_data_', '').replace(file_ext, '')

                    # 基本文件信息（不读取文件内容）
                    self.file_table.setItem(row, 0, QTableWidgetItem(filename))
                    self.file_table.setItem(row, 1, QTableWidgetItem(date_part))
                    self.file_table.setItem(row, 2, QTableWidgetItem(file_ext[1:]))

                    # 文件大小
                    try:
                        file_size = os.path.getsize(file_path)
                        size_mb = file_size / (1024 * 1024)
                        size_str = f"{size_mb:.2f} MB"
                        self.file_table.setItem(row, 4, QTableWidgetItem(size_str))
                    except:
                        self.file_table.setItem(row, 4, QTableWidgetItem("未知"))

                    # 修改时间
                    try:
                        mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                        mod_time_str = mod_time.strftime("%Y-%m-%d %H:%M:%S")
                        self.file_table.setItem(row, 5, QTableWidgetItem(mod_time_str))
                    except:
                        self.file_table.setItem(row, 5, QTableWidgetItem("未知"))

                    # 行数和状态设为待检测
                    self.file_table.setItem(row, 3, QTableWidgetItem("待检测"))
                    self.file_table.setItem(row, 6, QTableWidgetItem("待检测"))

                except Exception as e:
                    self.add_log(f"处理文件信息失败 {file_path}: {e}")
                    continue

            self.add_log(f"快速刷新文件列表完成，共 {len(files)} 个文件")

        except Exception as e:
            self.add_log(f"刷新文件列表失败: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")

    def check_selected_files(self):
        """检查选中文件的详细信息"""
        try:
            selected_rows = set()
            for item in self.file_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                QMessageBox.information(self, "提示", "请先选择要检查的文件")
                return

            self.add_log(f"开始检查 {len(selected_rows)} 个文件的详细信息...")

            data_dir = self.config_manager.get_data_dir()
            file_format = self.config_manager.get_file_format()
            data_manager = DataFileManager(data_dir, file_format)

            for row in selected_rows:
                filename_item = self.file_table.item(row, 0)
                if filename_item:
                    filename = filename_item.text()
                    file_path = os.path.join(data_dir, filename)

                    self.add_log(f"检查文件: {filename}")
                    file_info = data_manager.get_file_info(file_path)

                    if file_info:
                        # 更新行数
                        self.file_table.setItem(row, 3, QTableWidgetItem(str(file_info['row_count'])))

                        # 更新状态
                        status = file_info.get('status', 'unknown')
                        status_text = {
                            'ok': '正常',
                            'corrupted': '损坏',
                            'error': '错误',
                            'unsupported': '不支持',
                            'unknown': '未知'
                        }.get(status, status)

                        status_item = QTableWidgetItem(status_text)
                        if status == 'corrupted' or status == 'error':
                            status_item.setBackground(Qt.red)
                            status_item.setForeground(Qt.white)
                        elif status == 'ok':
                            status_item.setBackground(Qt.green)
                            status_item.setForeground(Qt.white)

                        self.file_table.setItem(row, 6, status_item)

            self.add_log("文件检查完成")

        except Exception as e:
            self.add_log(f"检查文件失败: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")

    def delete_selected_files(self):
        """删除选中的文件"""
        try:
            selected_rows = set()
            for item in self.file_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                QMessageBox.information(self, "提示", "请先选择要删除的文件")
                return

            # 确认删除
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除选中的 {len(selected_rows)} 个文件吗？\n此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 获取要删除的文件路径
            data_dir = self.config_manager.get_data_dir()
            deleted_count = 0

            for row in sorted(selected_rows, reverse=True):
                filename_item = self.file_table.item(row, 0)
                if filename_item:
                    filename = filename_item.text()
                    file_path = os.path.join(data_dir, filename)

                    if os.path.exists(file_path):
                        try:
                            os.remove(file_path)
                            deleted_count += 1
                            self.add_log(f"删除文件: {filename}")
                        except Exception as e:
                            self.add_log(f"删除文件失败 {filename}: {e}")

            # 刷新文件列表
            self.refresh_file_list()

            if deleted_count > 0:
                QMessageBox.information(self, "完成", f"成功删除 {deleted_count} 个文件")

        except Exception as e:
            self.add_log(f"删除文件出错: {e}")
            QMessageBox.critical(self, "错误", f"删除文件出错: {e}")

    def cleanup_corrupted_files(self):
        """清理损坏的文件"""
        try:
            # 获取数据文件管理器
            data_dir = self.config_manager.get_data_dir()
            file_format = self.config_manager.get_file_format()
            data_manager = DataFileManager(data_dir, file_format)

            # 获取文件列表
            files = data_manager.get_existing_files()

            corrupted_files = []
            for file_path in files:
                file_info = data_manager.get_file_info(file_path)
                if file_info and file_info.get('status') in ['corrupted', 'error']:
                    corrupted_files.append(file_path)

            if not corrupted_files:
                QMessageBox.information(self, "提示", "没有发现损坏的文件")
                return

            # 显示损坏文件列表并确认删除
            file_list = "\n".join([os.path.basename(f) for f in corrupted_files])
            reply = QMessageBox.question(
                self, "确认清理",
                f"发现 {len(corrupted_files)} 个损坏的文件：\n\n{file_list}\n\n确定要删除这些文件吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 删除损坏的文件
            deleted_count = 0
            for file_path in corrupted_files:
                try:
                    os.remove(file_path)
                    deleted_count += 1
                    self.add_log(f"删除损坏文件: {os.path.basename(file_path)}")
                except Exception as e:
                    self.add_log(f"删除损坏文件失败 {os.path.basename(file_path)}: {e}")

            # 刷新文件列表
            self.refresh_file_list()

            QMessageBox.information(self, "完成", f"成功清理 {deleted_count} 个损坏文件")

        except Exception as e:
            self.add_log(f"清理损坏文件出错: {e}")
            QMessageBox.critical(self, "错误", f"清理损坏文件出错: {e}")

    def start_download(self):
        """开始下载"""
        try:
            if self.download_worker and self.download_worker.isRunning():
                QMessageBox.warning(self, "警告", "下载任务已在运行中")
                return

            # 获取时间参数
            start_time = self.start_time_edit.dateTime().toString("yyyy-MM-dd hh:mm:ss")
            stop_time = None
            if self.enable_stop_time.isChecked():
                stop_time = self.stop_time_edit.dateTime().toString("yyyy-MM-dd hh:mm:ss")

            # 创建下载线程
            self.download_worker = DownloadWorker(self.download_manager, start_time, stop_time)
            self.download_worker.progress_updated.connect(self.on_progress_updated)
            self.download_worker.status_updated.connect(self.on_status_updated)
            self.download_worker.download_finished.connect(self.on_download_finished)

            # 更新界面状态
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度
            self.status_label.setText("正在下载...")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")

            # 启动下载
            self.download_worker.start()

            if stop_time:
                self.add_log(f"开始下载数据，时间范围: {start_time} 到 {stop_time}")
            else:
                self.add_log(f"开始下载数据，开始时间: {start_time}")

        except Exception as e:
            self.add_log(f"启动下载失败: {e}")
            QMessageBox.critical(self, "错误", f"启动下载失败: {e}")

    def stop_download(self):
        """停止下载"""
        try:
            if self.download_worker and self.download_worker.isRunning():
                self.download_manager.stop_download()
                self.add_log("正在停止下载...")
            else:
                self.add_log("当前没有下载任务在运行")

        except Exception as e:
            self.add_log(f"停止下载失败: {e}")

    def refresh_status(self):
        """刷新状态"""
        try:
            # 重新加载配置
            self.config = self.config_manager.get_config()

            # 更新上次更新时间
            last_update_time = self.config_manager.get_last_update_time()
            self.last_update_label.setText(last_update_time)

            # 刷新文件列表
            self.refresh_file_list()

            self.add_log("状态已刷新")

        except Exception as e:
            self.add_log(f"刷新状态失败: {e}")

    def on_progress_updated(self, progress_info: Dict):
        """处理进度更新"""
        try:
            self.pages_label.setText(f"页数: {progress_info.get('total_pages', 0)}")
            self.records_label.setText(f"记录数: {progress_info.get('total_records', 0)}")
            self.saved_label.setText(f"已保存: {progress_info.get('saved_records', 0)}")

        except Exception as e:
            self.add_log(f"更新进度显示失败: {e}")

    def on_status_updated(self, status: str):
        """处理状态更新"""
        self.add_log(status)

    def on_download_finished(self, success: bool):
        """处理下载完成"""
        try:
            # 更新界面状态
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.progress_bar.setVisible(False)

            if success:
                self.status_label.setText("下载完成")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")

                # 记录本次下载的时间范围
                start_time = self.start_time_edit.dateTime().toString("yyyy-MM-dd hh:mm:ss")
                if self.enable_stop_time.isChecked():
                    end_time = self.stop_time_edit.dateTime().toString("yyyy-MM-dd hh:mm:ss")
                else:
                    # 如果没有设置停止时间，使用当前时间
                    end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # 更新配置文件中的下载时间范围
                if self.config_manager.update_last_download_times(start_time, end_time):
                    self.add_log(f"已记录下载时间范围: {start_time} ~ {end_time}")

                # 刷新状态和文件列表
                self.refresh_status()
            else:
                self.status_label.setText("下载失败")
                self.status_label.setStyleSheet("color: red; font-weight: bold;")

        except Exception as e:
            self.add_log(f"处理下载完成事件失败: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")

    def update_display(self):
        """定时更新显示"""
        # 这里可以添加定期更新的逻辑
        pass

    def generate_statistics(self):
        """生成统计报告"""
        try:
            if self.statistics_worker and self.statistics_worker.isRunning():
                QMessageBox.warning(self, "警告", "统计任务已在运行中")
                return

            # 获取参数
            start_date = self.stats_start_date.date().toString("yyyy-MM-dd")
            end_date = self.stats_end_date.date().toString("yyyy-MM-dd")
            top_n = self.stats_top_n.value()

            # 验证日期范围
            if start_date > end_date:
                QMessageBox.warning(self, "错误", "开始日期不能晚于结束日期")
                return

            # 创建统计线程
            self.statistics_worker = StatisticsWorker(
                self.statistics_manager, start_date, end_date, top_n
            )
            self.statistics_worker.status_updated.connect(self.on_statistics_status_updated)
            self.statistics_worker.statistics_finished.connect(self.on_statistics_finished)

            # 更新界面状态
            self.stats_generate_button.setEnabled(False)
            self.stats_stop_button.setEnabled(True)
            self.stats_status_label.setText("正在生成统计报告...")
            self.stats_status_label.setStyleSheet("color: orange; font-weight: bold;")

            # 启动统计
            self.statistics_worker.start()
            self.add_log(f"开始生成统计报告: {start_date} 到 {end_date}, Top {top_n}")

        except Exception as e:
            self.add_log(f"启动统计分析失败: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")
            QMessageBox.critical(self, "错误", f"启动统计分析失败: {e}")

    def stop_statistics(self):
        """停止统计生成"""
        try:
            if self.statistics_worker and self.statistics_worker.isRunning():
                self.statistics_worker.terminate()
                self.statistics_worker.wait()

                # 更新界面状态
                self.stats_generate_button.setEnabled(True)
                self.stats_stop_button.setEnabled(False)
                self.stats_status_label.setText("统计已停止")
                self.stats_status_label.setStyleSheet("color: red; font-weight: bold;")

                self.add_log("统计生成已停止")
            else:
                self.add_log("没有正在运行的统计任务")

        except Exception as e:
            self.add_log(f"停止统计失败: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")

    def on_statistics_status_updated(self, status: str):
        """处理统计状态更新"""
        self.stats_status_label.setText(status)
        self.add_log(status)

    def on_statistics_finished(self, report: dict):
        """处理统计完成"""
        try:
            # 更新界面状态
            self.stats_generate_button.setEnabled(True)
            self.stats_stop_button.setEnabled(False)

            if 'error' in report:
                self.stats_status_label.setText("统计失败")
                self.stats_status_label.setStyleSheet("color: red; font-weight: bold;")
                self.add_log(f"统计失败: {report['error']}")
                QMessageBox.critical(self, "错误", f"统计失败: {report['error']}")
                return

            self.stats_status_label.setText("统计完成")
            self.stats_status_label.setStyleSheet("color: green; font-weight: bold;")
            self.download_nj_images_button.setEnabled(True)
            self.download_hy_images_button.setEnabled(True)
            self.stats_export_xlsx_button.setEnabled(True)
            self.stats_export_html_button.setEnabled(True)

            # 保存报告数据
            self.current_report = report

            # 显示统计结果
            self.display_statistics_report(report)
            self.add_log("统计报告生成完成")

        except Exception as e:
            self.add_log(f"处理统计结果失败: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")

    def display_statistics_report(self, report: dict):
        """显示统计报告（按地区分类）"""
        try:
            regions_data = report.get('regions', {})

            for region_name, region_data in regions_data.items():
                if region_name in self.region_stats:
                    self.add_log(f"显示{region_name}地区统计结果...")

                    # 显示总览统计
                    self.display_region_overview_statistics(
                        region_name, region_data.get('basic_stats', {})
                    )

                    # 显示各类缺陷统计
                    self.display_region_defect_statistics(
                        region_name, region_data.get('top_aoi_results', [])
                    )

                    # 显示代码TOP N统计
                    self.display_region_code_statistics(
                        region_name, region_data.get('cross_statistics', [])
                    )

        except Exception as e:
            self.add_log(f"显示统计报告失败: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")

    def display_region_overview_statistics(self, region_name: str, basic_stats: dict):
        """显示地区总览统计信息"""
        try:
            text = f"=== {region_name}地区总览统计 ===\n\n"
            text += f"总数据量: {basic_stats.get('total_count', 0):,} 条\n"
            text += f"AI算法覆盖: {basic_stats.get('ai_covered_count', 0):,} 条\n"
            text += f"AI覆盖率: {basic_stats.get('ai_coverage_rate', 0):.2f}%\n"
            text += f"AI判定数量: {basic_stats.get('ai_judged_count', 0):,} 条\n"
            text += f"AI判定率: {basic_stats.get('ai_judge_rate', 0):.2f}%\n"
            text += f"AI判定OK: {basic_stats.get('ai_ok_count', 0):,} 条\n"
            text += f"AI判定NG: {basic_stats.get('ai_ng_count', 0):,} 条\n"

            if region_name in self.region_stats:
                self.region_stats[region_name]['overview_text'].setText(text)

        except Exception as e:
            self.add_log(f"显示{region_name}地区总览统计失败: {e}")

    def display_region_defect_statistics(self, region_name: str, aoi_results: list):
        """显示地区各类缺陷统计"""
        try:
            if region_name not in self.region_stats:
                return

            table = self.region_stats[region_name]['defect_table']

            # 设置表格
            table.setRowCount(len(aoi_results))
            table.setColumnCount(8)
            table.setHorizontalHeaderLabels([
                "缺陷类型", "数量", "占比(%)", "AI覆盖数", "AI覆盖率(%)",
                "AI判定数", "AI判定率(%)", "AI判定OK/NG"
            ])

            # 填充数据
            for row, item in enumerate(aoi_results):
                table.setItem(row, 0, QTableWidgetItem(item['name']))
                table.setItem(row, 1, QTableWidgetItem(str(item['count'])))
                table.setItem(row, 2, QTableWidgetItem(f"{item['percentage']:.2f}"))

                # AI相关数据
                ai_covered = item.get('ai_covered_count', 0)
                ai_coverage_rate = item.get('ai_coverage_rate', 0)
                ai_judged = item.get('ai_judged_count', 0)
                ai_judge_rate = item.get('ai_judge_rate', 0)
                ai_ok = item.get('ai_ok_count', 0)
                ai_ng = item.get('ai_ng_count', 0)

                table.setItem(row, 3, QTableWidgetItem(str(ai_covered)))
                table.setItem(row, 4, QTableWidgetItem(f"{ai_coverage_rate:.2f}"))
                table.setItem(row, 5, QTableWidgetItem(str(ai_judged)))
                table.setItem(row, 6, QTableWidgetItem(f"{ai_judge_rate:.2f}"))
                table.setItem(row, 7, QTableWidgetItem(f"OK:{ai_ok}/NG:{ai_ng}"))

            # 调整列宽
            header = table.horizontalHeader()
            header.setSectionResizeMode(QHeaderView.ResizeToContents)

        except Exception as e:
            self.add_log(f"显示{region_name}地区缺陷统计失败: {e}")

    def display_region_code_statistics(self, region_name: str, cross_stats: list):
        """显示地区代码TOP N统计"""
        try:
            if region_name not in self.region_stats:
                return

            table = self.region_stats[region_name]['code_table']

            # 设置表格
            table.setRowCount(len(cross_stats))
            table.setColumnCount(12)
            table.setHorizontalHeaderLabels([
                "materialCode", "libParts", "AOI检测结果", "数量", "占比(%)",
                "AI判定数", "AI判定率(%)", "未AI判定数", "AI覆盖数", "AI覆盖率(%)",
                "时间范围", "主要线体"
            ])

            # 填充数据
            for row, item in enumerate(cross_stats):
                table.setItem(row, 0, QTableWidgetItem(item.get('materialcode', '')))
                table.setItem(row, 1, QTableWidgetItem(item.get('libparts', '')))
                table.setItem(row, 2, QTableWidgetItem(item.get('aoijudgeresult', '')))
                table.setItem(row, 3, QTableWidgetItem(str(item['count'])))
                table.setItem(row, 4, QTableWidgetItem(f"{item['percentage']:.2f}"))

                # AI相关数据
                ai_judged = item.get('ai_judged_count', 0)
                ai_judge_rate = item.get('ai_judge_rate', 0)
                ai_not_judged = item.get('ai_not_judged_count', 0)
                ai_covered = item.get('ai_covered_count', 0)
                ai_coverage_rate = item.get('ai_coverage_rate', 0)
                time_range = item.get('time_range', '')
                top_lines = item.get('top_lines', [])

                # 按新的列顺序填充数据
                table.setItem(row, 5, QTableWidgetItem(str(ai_judged)))
                table.setItem(row, 6, QTableWidgetItem(f"{ai_judge_rate:.2f}"))
                table.setItem(row, 7, QTableWidgetItem(str(ai_not_judged)))
                table.setItem(row, 8, QTableWidgetItem(str(ai_covered)))
                table.setItem(row, 9, QTableWidgetItem(f"{ai_coverage_rate:.2f}"))
                table.setItem(row, 10, QTableWidgetItem(time_range))
                table.setItem(row, 11, QTableWidgetItem(', '.join(top_lines[:3])))

            # 调整列宽
            header = table.horizontalHeader()
            header.setSectionResizeMode(QHeaderView.ResizeToContents)

        except Exception as e:
            self.add_log(f"显示{region_name}地区代码统计失败: {e}")

    def display_overview_statistics(self, basic_stats: dict):
        """显示总览统计信息"""
        try:
            text = "=== 总览统计信息 ===\n\n"
            text += f"总数据量: {basic_stats.get('total_count', 0):,} 条\n"
            text += f"AI算法覆盖: {basic_stats.get('ai_covered_count', 0):,} 条\n"
            text += f"AI覆盖率: {basic_stats.get('ai_coverage_rate', 0):.2f}%\n"
            text += f"AI判定数量: {basic_stats.get('ai_judged_count', 0):,} 条\n"
            text += f"AI判定率: {basic_stats.get('ai_judge_rate', 0):.2f}%\n"
            text += f"AI判定OK: {basic_stats.get('ai_ok_count', 0):,} 条\n"
            text += f"AI判定NG: {basic_stats.get('ai_ng_count', 0):,} 条\n"

            self.overview_stats_text.setText(text)

        except Exception as e:
            self.add_log(f"显示总览统计失败: {e}")

    def display_defect_statistics(self, report: dict):
        """显示各类缺陷统计"""
        try:
            aoi_results = report.get('top_aoi_results', [])

            # 设置表格
            self.defect_stats_table.setRowCount(len(aoi_results))
            self.defect_stats_table.setColumnCount(6)
            self.defect_stats_table.setHorizontalHeaderLabels([
                "缺陷类型", "数量", "占比(%)", "AI覆盖数", "AI覆盖率(%)", "AI判定数"
            ])

            # 填充数据
            for row, item in enumerate(aoi_results):
                self.defect_stats_table.setItem(row, 0, QTableWidgetItem(item['name']))
                self.defect_stats_table.setItem(row, 1, QTableWidgetItem(str(item['count'])))
                self.defect_stats_table.setItem(row, 2, QTableWidgetItem(f"{item['percentage']:.2f}"))

                # AI相关数据（如果有的话）
                ai_covered = item.get('ai_covered_count', 0)
                ai_coverage_rate = item.get('ai_coverage_rate', 0)
                ai_judged = item.get('ai_judged_count', 0)

                self.defect_stats_table.setItem(row, 3, QTableWidgetItem(str(ai_covered)))
                self.defect_stats_table.setItem(row, 4, QTableWidgetItem(f"{ai_coverage_rate:.2f}"))
                self.defect_stats_table.setItem(row, 5, QTableWidgetItem(str(ai_judged)))

            # 调整列宽
            header = self.defect_stats_table.horizontalHeader()
            header.setSectionResizeMode(QHeaderView.ResizeToContents)

        except Exception as e:
            self.add_log(f"显示各类缺陷统计失败: {e}")

    def display_code_statistics(self, cross_stats: list):
        """显示代码TOP N统计"""
        try:
            # 设置表格
            self.code_stats_table.setRowCount(len(cross_stats))
            self.code_stats_table.setColumnCount(8)
            self.code_stats_table.setHorizontalHeaderLabels([
                "物料代码", "libParts", "AOI检测结果", "数量", "占比(%)",
                "AI覆盖数", "AI判定数", "主要线体"
            ])

            # 填充数据
            for row, item in enumerate(cross_stats):
                self.code_stats_table.setItem(row, 0, QTableWidgetItem(item.get('material_code', '')))
                self.code_stats_table.setItem(row, 1, QTableWidgetItem(item.get('lib_parts', '')))
                self.code_stats_table.setItem(row, 2, QTableWidgetItem(item['aoi_result']))
                self.code_stats_table.setItem(row, 3, QTableWidgetItem(str(item['count'])))
                self.code_stats_table.setItem(row, 4, QTableWidgetItem(f"{item['percentage']:.2f}"))

                # AI相关数据
                ai_covered = item.get('ai_covered_count', 0)
                ai_judged = item.get('ai_judged_count', 0)
                top_lines = item.get('top_lines', [])

                self.code_stats_table.setItem(row, 5, QTableWidgetItem(str(ai_covered)))
                self.code_stats_table.setItem(row, 6, QTableWidgetItem(str(ai_judged)))
                self.code_stats_table.setItem(row, 7, QTableWidgetItem(', '.join(top_lines[:3])))

            # 调整列宽
            header = self.code_stats_table.horizontalHeader()
            header.setSectionResizeMode(QHeaderView.ResizeToContents)

        except Exception as e:
            self.add_log(f"显示代码TOP N统计失败: {e}")

    def export_xlsx_report(self):
        """导出XLSX报告"""
        try:
            if not hasattr(self, 'current_report'):
                QMessageBox.warning(self, "警告", "请先生成统计报告")
                return

            # 生成文件名（使用统计日期范围）
            start_date = self.stats_start_date.date().toString("yyyyMMdd")
            end_date = self.stats_end_date.date().toString("yyyyMMdd")
            if start_date == end_date:
                filename = f"AOI统计报告_{start_date}.xlsx"
            else:
                filename = f"AOI统计报告_{start_date}_{end_date}.xlsx"

            export_dir = self.config_manager.get_export_dir()
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)

            file_path = os.path.join(export_dir, filename)

            # 导出Excel报告
            self.export_to_excel(self.current_report, file_path)

            self.add_log(f"XLSX报告导出成功: {file_path}")
            QMessageBox.information(self, "成功", f"XLSX报告导出成功\n文件路径: {file_path}")

        except Exception as e:
            self.add_log(f"导出XLSX报告失败: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")
            QMessageBox.critical(self, "错误", f"导出XLSX报告失败: {e}")

    def export_to_excel(self, report: dict, file_path: str):
        """导出报告到Excel文件"""
        try:
            import pandas as pd

            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                regions_data = report.get('regions', {})

                # 首先添加TOP N处理情况表格（南京和河源）
                date_range = report.get('date_range', {})
                top_processing_sheets = self.export_top_processing_sheets(writer, regions_data, date_range)

                for region_name, region_data in regions_data.items():
                    # 基础统计 - 按界面顺序
                    basic_stats = region_data.get('basic_stats', {})
                    if basic_stats:
                        basic_ordered_data = self.format_basic_stats_for_excel(basic_stats)
                        basic_df = pd.DataFrame([basic_ordered_data])
                        basic_df.to_excel(writer, sheet_name=f'{region_name}_总览', index=False)

                    # 各类缺陷统计 - 按界面列顺序
                    defect_stats = region_data.get('top_aoi_results', [])
                    if defect_stats:
                        defect_ordered_data = self.format_defect_stats_for_excel(defect_stats)
                        defect_df = pd.DataFrame(defect_ordered_data)
                        defect_df.to_excel(writer, sheet_name=f'{region_name}_缺陷统计', index=False)

                    # 代码TOP N统计 - 按界面列顺序
                    code_stats = region_data.get('cross_statistics', [])
                    if code_stats:
                        code_ordered_data = self.format_code_stats_for_excel(code_stats)
                        code_df = pd.DataFrame(code_ordered_data)
                        code_df.to_excel(writer, sheet_name=f'{region_name}_代码统计', index=False)

            # 在with语句结束后插入图片
            for sheet_info in top_processing_sheets:
                self.insert_images_to_excel_file(
                    file_path,
                    sheet_info['sheet_name'],
                    sheet_info['cross_stats'],
                    sheet_info['start_date'],
                    sheet_info['end_date'],
                    sheet_info['region_name']
                )

            self.add_log(f"Excel文件写入完成: {file_path}")

        except Exception as e:
            self.add_log(f"写入Excel文件失败: {e}")
            raise

    def export_html_report(self):
        """导出HTML报告"""
        try:
            if not hasattr(self, 'current_report'):
                QMessageBox.warning(self, "警告", "请先生成统计报告")
                return

            # 生成文件名（使用统计日期范围）
            start_date = self.stats_start_date.date().toString("yyyyMMdd")
            end_date = self.stats_end_date.date().toString("yyyyMMdd")
            if start_date == end_date:
                filename = f"AOI统计报告_{start_date}.html"
            else:
                filename = f"AOI统计报告_{start_date}_{end_date}.html"

            export_dir = self.config_manager.get_export_dir()
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)

            file_path = os.path.join(export_dir, filename)

            # 导出HTML报告
            self.export_to_html(self.current_report, file_path)

            self.add_log(f"HTML报告导出成功: {file_path}")
            QMessageBox.information(self, "成功", f"HTML报告导出成功\n文件路径: {file_path}")

        except Exception as e:
            self.add_log(f"导出HTML报告失败: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")
            QMessageBox.critical(self, "错误", f"导出HTML报告失败: {e}")

    def export_to_html(self, report: dict, file_path: str):
        """导出报告到HTML文件"""
        try:
            html_content = self.generate_html_report(report)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            self.add_log(f"HTML文件写入完成: {file_path}")

        except Exception as e:
            self.add_log(f"写入HTML文件失败: {e}")
            raise

    def generate_html_report(self, report: dict) -> str:
        """生成HTML报告内容"""
        try:
            date_range = report.get('date_range', {})
            generated_at = report.get('generated_at', '')
            regions_data = report.get('regions', {})

            html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AOI统计报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .region {{ margin-bottom: 40px; border: 1px solid #ddd; padding: 20px; }}
        .region h2 {{ color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }}
        .top-tables {{ margin-bottom: 40px; }}
        .top-table {{ margin-bottom: 30px; border: 1px solid #ddd; padding: 20px; }}
        .top-table h2 {{ color: #333; border-bottom: 2px solid #ff6b35; padding-bottom: 10px; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; font-weight: bold; }}
        .stats-overview {{ background-color: #f8f9fa; padding: 15px; margin: 10px 0; }}
        .stats-item {{ margin: 5px 0; }}
        .processing-table th {{ background-color: #fff3cd; }}
        .processing-table .empty-cell {{ background-color: #f8f9fa; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>AOI数据统计分析报告</h1>
        <p>统计时间范围: {date_range.get('start', '')} ~ {date_range.get('end', '')}</p>
        <p>生成时间: {generated_at}</p>
    </div>
"""

            # 添加南京和河源地区的TOP N处理情况表格
            html += self.generate_top_processing_tables(regions_data)

            for region_name, region_data in regions_data.items():
                html += f"""
    <div class="region">
        <h2>{region_name}地区统计</h2>
"""

                # 基础统计
                basic_stats = region_data.get('basic_stats', {})
                html += f"""
        <h3>总览统计</h3>
        <div class="stats-overview">
            <div class="stats-item">总数据量: {basic_stats.get('total_count', 0):,} 条</div>
            <div class="stats-item">AI算法覆盖: {basic_stats.get('ai_covered_count', 0):,} 条</div>
            <div class="stats-item">AI覆盖率: {basic_stats.get('ai_coverage_rate', 0):.2f}%</div>
            <div class="stats-item">AI判定数量: {basic_stats.get('ai_judged_count', 0):,} 条</div>
            <div class="stats-item">AI判定率: {basic_stats.get('ai_judge_rate', 0):.2f}%</div>
            <div class="stats-item">AI判定OK: {basic_stats.get('ai_ok_count', 0):,} 条</div>
            <div class="stats-item">AI判定NG: {basic_stats.get('ai_ng_count', 0):,} 条</div>
        </div>
"""

                # 各类缺陷统计
                defect_stats = region_data.get('top_aoi_results', [])
                if defect_stats:
                    html += """
        <h3>各类缺陷统计</h3>
        <table>
            <tr>
                <th>缺陷类型</th>
                <th>数量</th>
                <th>占比(%)</th>
                <th>AI覆盖数</th>
                <th>AI覆盖率(%)</th>
                <th>AI判定数</th>
                <th>AI判定率(%)</th>
                <th>AI判定OK/NG</th>
            </tr>
"""
                    for item in defect_stats:
                        ai_ok = item.get('ai_ok_count', 0)
                        ai_ng = item.get('ai_ng_count', 0)
                        html += f"""
            <tr>
                <td>{item['name']}</td>
                <td>{item['count']}</td>
                <td>{item['percentage']:.2f}</td>
                <td>{item.get('ai_covered_count', 0)}</td>
                <td>{item.get('ai_coverage_rate', 0):.2f}</td>
                <td>{item.get('ai_judged_count', 0)}</td>
                <td>{item.get('ai_judge_rate', 0):.2f}</td>
                <td>OK:{ai_ok}/NG:{ai_ng}</td>
            </tr>
"""
                    html += "        </table>\n"

                # 代码TOP N统计
                code_stats = region_data.get('cross_statistics', [])
                if code_stats:
                    html += """
        <h3>代码TOP N统计</h3>
        <table>
            <tr>
                <th>materialCode</th>
                <th>libParts</th>
                <th>AOI检测结果</th>
                <th>数量</th>
                <th>占比(%)</th>
                <th>AI覆盖数</th>
                <th>AI覆盖率(%)</th>
                <th>AI判定数</th>
                <th>时间范围</th>
                <th>主要线体</th>
            </tr>
"""
                    for item in code_stats:
                        top_lines = item.get('top_lines', [])
                        html += f"""
            <tr>
                <td>{item.get('materialcode', '')}</td>
                <td>{item.get('libparts', '')}</td>
                <td>{item.get('aoijudgeresult', '')}</td>
                <td>{item['count']}</td>
                <td>{item['percentage']:.2f}</td>
                <td>{item.get('ai_covered_count', 0)}</td>
                <td>{item.get('ai_coverage_rate', 0):.2f}</td>
                <td>{item.get('ai_judged_count', 0)}</td>
                <td>{item.get('time_range', '')}</td>
                <td>{', '.join(top_lines[:3])}</td>
            </tr>
"""
                    html += "        </table>\n"

                html += "    </div>\n"

            html += """
</body>
</html>
"""

            return html

        except Exception as e:
            self.add_log(f"生成HTML内容失败: {e}")
            raise

    def generate_top_processing_tables(self, regions_data: dict) -> str:
        """生成南京和河源地区的TOP N处理情况表格"""
        try:
            html = """
    <div class="top-tables">
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">TOP N处理情况表格</h1>
"""

            # 处理南京和河源地区
            target_regions = ['南京', '河源']

            for region_name in target_regions:
                if region_name not in regions_data:
                    continue

                region_data = regions_data[region_name]
                cross_stats = region_data.get('cross_statistics', [])

                if not cross_stats:
                    continue

                # 获取实际的N值
                actual_n = len(cross_stats)

                html += f"""
        <div class="top-table">
            <h2>{region_name}地区TOP {actual_n}处理情况</h2>
            <table class="processing-table">
                <tr>
                    <th>materialCode</th>
                    <th>libParts</th>
                    <th>AOI检测结果</th>
                    <th>数量</th>
                    <th>占比(%)</th>
                    <th>AI判定数</th>
                    <th>AI判定率(%)</th>
                    <th>图片1</th>
                    <th>图片2</th>
                    <th>图片3</th>
                    <th>图片4</th>
                    <th>图片5</th>
                    <th>图片6</th>
                    <th>负责人</th>
                    <th>决策</th>
                    <th>进度</th>
                    <th>进展</th>
                    <th>时间范围</th>
                    <th>主要线体</th>
                </tr>
"""

                # 填充数据行
                for item in cross_stats:
                    material_code = item.get('materialcode', '')
                    lib_parts = item.get('libparts', '')
                    aoi_result = item.get('aoijudgeresult', '')
                    count = item.get('count', 0)
                    percentage = item.get('percentage', 0)
                    ai_judged = item.get('ai_judged_count', 0)
                    ai_judge_rate = item.get('ai_judge_rate', 0)
                    time_range = item.get('time_range', '')
                    top_lines = item.get('top_lines', [])
                    top_lines_str = ', '.join(top_lines[:3])

                    # 查找对应的6张图片文件
                    image_cells = []
                    if hasattr(self, 'current_report'):
                        date_range = self.current_report.get('date_range', {})
                        start_date = date_range.get('start', '')
                        end_date = date_range.get('end', '')
                        if start_date and end_date and lib_parts:
                            image_downloader = ImageDownloader()
                            all_images = image_downloader.find_all_images_for_libparts(
                                lib_parts, start_date, end_date, region_name, aoi_result, "pics"
                            )

                            # 为image1到image6生成HTML单元格
                            for image_key in ['image1', 'image2', 'image3', 'image4', 'image5', 'image6']:
                                if image_key in all_images and os.path.exists(all_images[image_key]):
                                    rel_path = os.path.relpath(all_images[image_key])
                                    image_cell = f'<img src="{rel_path}" style="max-width:80px;max-height:80px;" alt="{image_key}">'
                                else:
                                    image_cell = '<span class="empty-cell">无图片</span>'
                                image_cells.append(image_cell)

                    # 如果没有找到图片，填充空单元格
                    while len(image_cells) < 6:
                        image_cells.append('<span class="empty-cell">无图片</span>')

                    html += f"""
                <tr>
                    <td>{material_code}</td>
                    <td>{lib_parts}</td>
                    <td>{aoi_result}</td>
                    <td>{count}</td>
                    <td>{percentage:.2f}</td>
                    <td>{ai_judged}</td>
                    <td>{ai_judge_rate:.2f}</td>
                    <td>{image_cells[0]}</td>
                    <td>{image_cells[1]}</td>
                    <td>{image_cells[2]}</td>
                    <td>{image_cells[3]}</td>
                    <td>{image_cells[4]}</td>
                    <td>{image_cells[5]}</td>
                    <td class="empty-cell"></td>
                    <td class="empty-cell"></td>
                    <td class="empty-cell"></td>
                    <td class="empty-cell"></td>
                    <td>{time_range}</td>
                    <td>{top_lines_str}</td>
                </tr>
"""

                html += """
            </table>
        </div>
"""

            html += """
    </div>
"""

            return html

        except Exception as e:
            self.add_log(f"生成TOP N处理情况表格失败: {e}")
            return ""

    def export_top_processing_sheets(self, writer, regions_data: dict, date_range: dict):
        """导出TOP N处理情况表格到Excel"""
        try:
            import pandas as pd

            # 获取日期范围
            start_date = date_range.get('start', '')
            end_date = date_range.get('end', '')

            # 处理南京和河源地区
            target_regions = ['南京', '河源']
            sheets_info = []  # 存储需要插入图片的工作表信息

            for region_name in target_regions:
                if region_name not in regions_data:
                    continue

                region_data = regions_data[region_name]
                cross_stats = region_data.get('cross_statistics', [])

                if not cross_stats:
                    continue

                # 获取实际的N值
                actual_n = len(cross_stats)

                # 按指定列顺序格式化数据
                formatted_data = []
                for item in cross_stats:
                    libparts = item.get('libparts', '')

                    row_data = {
                        'materialCode': item.get('materialcode', ''),
                        'libParts': libparts,
                        'AOI检测结果': item.get('aoijudgeresult', ''),
                        '数量': item.get('count', 0),
                        '占比(%)': round(item.get('percentage', 0), 2),
                        'AI判定数': item.get('ai_judged_count', 0),
                        'AI判定率(%)': round(item.get('ai_judge_rate', 0), 2),
                        '图片1': '',  # 将在后续插入图片
                        '图片2': '',
                        '图片3': '',
                        '图片4': '',
                        '图片5': '',
                        '图片6': '',
                        '负责人': '',  # 空列
                        '决策': '',  # 空列
                        '进度': '',  # 空列
                        '进展': '',  # 空列
                        '时间范围': item.get('time_range', ''),
                        '主要线体': ', '.join(item.get('top_lines', [])[:3])
                    }
                    formatted_data.append(row_data)

                # 创建DataFrame并导出
                df = pd.DataFrame(formatted_data)
                sheet_name = f'{region_name}_TOP{actual_n}处理情况'
                df.to_excel(writer, sheet_name=sheet_name, index=False)

                # 保存工作表信息，稍后插入图片
                sheets_info.append({
                    'sheet_name': sheet_name,
                    'cross_stats': cross_stats,
                    'start_date': start_date,
                    'end_date': end_date,
                    'region_name': region_name
                })

            return sheets_info

        except Exception as e:
            self.add_log(f"导出TOP N处理情况表格失败: {e}")
            return []

    def format_basic_stats_for_excel(self, basic_stats: dict) -> dict:
        """格式化基础统计数据，按界面显示顺序"""
        return {
            '总数据量': basic_stats.get('total_count', 0),
            'AI算法覆盖': basic_stats.get('ai_covered_count', 0),
            'AI覆盖率(%)': round(basic_stats.get('ai_coverage_rate', 0), 2),
            'AI判定数量': basic_stats.get('ai_judged_count', 0),
            'AI判定率(%)': round(basic_stats.get('ai_judge_rate', 0), 2),
            'AI判定OK': basic_stats.get('ai_ok_count', 0),
            'AI判定NG': basic_stats.get('ai_ng_count', 0)
        }

    def format_defect_stats_for_excel(self, defect_stats: list) -> list:
        """格式化缺陷统计数据，按界面列顺序"""
        formatted_data = []
        for item in defect_stats:
            row_data = {
                '缺陷类型': item.get('name', ''),
                '数量': item.get('count', 0),
                '占比(%)': round(item.get('percentage', 0), 2),
                'AI覆盖数': item.get('ai_covered_count', 0),
                'AI覆盖率(%)': round(item.get('ai_coverage_rate', 0), 2),
                'AI判定数': item.get('ai_judged_count', 0),
                'AI判定率(%)': round(item.get('ai_judge_rate', 0), 2),
                'AI判定OK': item.get('ai_ok_count', 0),
                'AI判定NG': item.get('ai_ng_count', 0)
            }
            formatted_data.append(row_data)
        return formatted_data

    def format_code_stats_for_excel(self, code_stats: list) -> list:
        """格式化代码统计数据，按界面列顺序"""
        formatted_data = []
        for item in code_stats:
            row_data = {
                'materialCode': item.get('materialcode', ''),
                'libParts': item.get('libparts', ''),
                'AOI检测结果': item.get('aoijudgeresult', ''),
                '数量': item.get('count', 0),
                '占比(%)': round(item.get('percentage', 0), 2),
                'AI判定数': item.get('ai_judged_count', 0),
                'AI判定率(%)': round(item.get('ai_judge_rate', 0), 2),
                '时间范围': item.get('time_range', ''),
                '主要线体': ', '.join(item.get('top_lines', [])[:3])
            }
            formatted_data.append(row_data)
        return formatted_data

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 检查是否有任务在运行
            running_tasks = []
            if self.download_worker and self.download_worker.isRunning():
                running_tasks.append("下载任务")
            if self.statistics_worker and self.statistics_worker.isRunning():
                running_tasks.append("统计任务")

            if running_tasks:
                task_list = "、".join(running_tasks)
                reply = QMessageBox.question(
                    self, "确认关闭",
                    f"{task_list}正在运行，确定要关闭程序吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    if self.download_worker and self.download_worker.isRunning():
                        self.download_manager.stop_download()
                        self.download_worker.wait(3000)
                    if self.statistics_worker and self.statistics_worker.isRunning():
                        self.statistics_worker.terminate()
                        self.statistics_worker.wait(3000)
                    event.accept()
                else:
                    event.ignore()
            else:
                event.accept()

        except Exception as e:
            self.add_log(f"关闭程序时出错: {e}")
            event.accept()

    def download_nanjing_images(self):
        """下载南京地区图片"""
        try:
            if not hasattr(self, 'current_report'):
                QMessageBox.warning(self, "警告", "请先生成统计报告")
                return

            # 获取南京地区数据
            regions_data = self.current_report.get('regions', {})
            nanjing_data = regions_data.get('南京', {})
            cross_statistics = nanjing_data.get('cross_statistics', [])

            if not cross_statistics:
                QMessageBox.warning(self, "警告", "南京地区没有代码统计数据")
                return

            # 获取统计日期范围
            date_range = self.current_report.get('date_range', {})
            start_date = date_range.get('start', '')
            end_date = date_range.get('end', '')

            if not start_date or not end_date:
                QMessageBox.warning(self, "警告", "无法获取统计日期范围")
                return

            # 禁用按钮，显示进度
            self.download_nj_images_button.setEnabled(False)
            self.download_nj_images_button.setText("下载中...")

            # 获取数据文件列表
            data_files = self.get_data_files_for_date_range(start_date, end_date)

            if not data_files:
                QMessageBox.warning(self, "警告", "未找到对应日期的数据文件")
                self.download_nj_images_button.setEnabled(True)
                self.download_nj_images_button.setText("下载南京图片")
                return

            # 创建图片下载器
            image_downloader = ImageDownloader()

            # 下载图片
            self.add_log("开始下载南京地区图片...")
            libparts_to_images = image_downloader.download_region_images(
                cross_statistics, start_date, end_date, data_files, "南京", "pics"
            )

            # 恢复按钮状态
            self.download_nj_images_button.setEnabled(True)
            self.download_nj_images_button.setText("下载南京图片")

            if libparts_to_images:
                self.add_log(f"南京地区图片下载完成，共下载 {len(libparts_to_images)} 个代码的图片")
                QMessageBox.information(
                    self, "成功",
                    f"南京地区图片下载完成\n共下载 {len(libparts_to_images)} 个代码的图片\n保存路径: pics文件夹"
                )
            else:
                self.add_log("南京地区图片下载失败，未找到任何图片")
                QMessageBox.warning(self, "警告", "未找到任何图片，请检查数据文件和网络连接")

        except Exception as e:
            self.add_log(f"下载南京地区图片失败: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")
            QMessageBox.critical(self, "错误", f"下载南京地区图片失败: {e}")

            # 恢复按钮状态
            self.download_nj_images_button.setEnabled(True)
            self.download_nj_images_button.setText("下载南京图片")

    def download_heyuan_images(self):
        """下载河源地区图片"""
        try:
            if not hasattr(self, 'current_report'):
                QMessageBox.warning(self, "警告", "请先生成统计报告")
                return

            # 获取河源地区的交叉统计数据
            regions_data = self.current_report.get('regions', {})
            if '河源' not in regions_data:
                QMessageBox.warning(self, "警告", "当前报告中没有河源地区数据")
                return

            cross_statistics = regions_data['河源'].get('cross_statistics', [])
            if not cross_statistics:
                QMessageBox.warning(self, "警告", "河源地区没有交叉统计数据")
                return

            # 获取统计日期范围
            date_range = self.current_report.get('date_range', {})
            start_date = date_range.get('start', '')
            end_date = date_range.get('end', '')

            if not start_date or not end_date:
                QMessageBox.warning(self, "警告", "无法获取统计日期范围")
                return

            # 禁用按钮，显示进度
            self.download_hy_images_button.setEnabled(False)
            self.download_hy_images_button.setText("下载中...")

            # 获取数据文件列表
            data_files = self.get_data_files_for_date_range(start_date, end_date)

            if not data_files:
                QMessageBox.warning(self, "警告", "未找到对应日期的数据文件")
                self.download_hy_images_button.setEnabled(True)
                self.download_hy_images_button.setText("下载河源图片")
                return

            # 创建图片下载器
            image_downloader = ImageDownloader()

            # 下载图片
            self.add_log("开始下载河源地区图片...")
            libparts_to_images = image_downloader.download_region_images(
                cross_statistics, start_date, end_date, data_files, "河源", "pics"
            )

            # 恢复按钮状态
            self.download_hy_images_button.setEnabled(True)
            self.download_hy_images_button.setText("下载河源图片")

            if libparts_to_images:
                self.add_log(f"河源地区图片下载完成，共下载 {len(libparts_to_images)} 个代码的图片")
                QMessageBox.information(
                    self, "成功",
                    f"河源地区图片下载完成\n共下载 {len(libparts_to_images)} 个代码的图片\n保存路径: pics文件夹"
                )
            else:
                QMessageBox.warning(self, "警告", "河源地区图片下载失败，请检查网络连接和数据文件")

        except Exception as e:
            self.add_log(f"下载河源地区图片失败: {e}")
            import traceback
            self.add_log(f"详细错误: {traceback.format_exc()}")
            QMessageBox.critical(self, "错误", f"下载河源地区图片失败: {e}")

            # 恢复按钮状态
            self.download_hy_images_button.setEnabled(True)
            self.download_hy_images_button.setText("下载河源图片")

    def get_data_files_for_date_range(self, start_date: str, end_date: str) -> list:
        """获取日期范围内的数据文件列表"""
        try:
            data_files = []
            data_dir = "data"

            if not os.path.exists(data_dir):
                return data_files

            # 解析日期
            from datetime import datetime, timedelta
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")

            # 遍历日期范围内的所有日期
            current_dt = start_dt
            while current_dt <= end_dt:
                date_str = current_dt.strftime("%Y-%m-%d")
                file_path = os.path.join(data_dir, f"aoi_data_{date_str}.xlsx")

                if os.path.exists(file_path):
                    data_files.append(file_path)

                current_dt += timedelta(days=1)

            return data_files

        except Exception as e:
            self.add_log(f"获取数据文件列表失败: {e}")
            return []

    def insert_images_to_excel_sheet(self, writer, sheet_name: str, cross_stats: list,
                                   start_date: str, end_date: str, region_name: str):
        """在Excel工作表中插入图片"""
        try:
            from openpyxl.drawing.image import Image as OpenpyxlImage

            # 获取工作表
            workbook = writer.book
            worksheet = workbook[sheet_name]

            # 图片列的起始列号（图片1到图片6对应列H到M）
            image_columns = ['H', 'I', 'J', 'K', 'L', 'M']
            image_keys = ['image1', 'image2', 'image3', 'image4', 'image5', 'image6']

            # 设置行高和列宽以适应图片
            for col in image_columns:
                worksheet.column_dimensions[col].width = 15

            # 为每行数据插入图片
            for row_idx, item in enumerate(cross_stats):
                libparts = item.get('libparts', '')
                aoi_result = item.get('aoijudgeresult', '')
                if not libparts:
                    continue

                # 查找该libParts的所有图片（精确匹配地区和AOI检测结果）
                image_downloader = ImageDownloader()
                all_images = image_downloader.find_all_images_for_libparts(
                    libparts, start_date, end_date, region_name, aoi_result, "pics"
                )

                self.logger.info(f"查找图片: libParts={libparts}, region={region_name}, aoi_result={aoi_result}, 找到{len(all_images)}张图片")

                # Excel行号（从第2行开始，因为第1行是表头）
                excel_row = row_idx + 2
                worksheet.row_dimensions[excel_row].height = 100  # 设置行高

                # 插入每张图片
                for image_key, col in zip(image_keys, image_columns):
                    if image_key in all_images:
                        image_path = all_images[image_key]
                        if os.path.exists(image_path):
                            try:
                                # 创建图片对象
                                img = OpenpyxlImage(image_path)

                                # 调整图片大小
                                img.width = 100
                                img.height = 100

                                # 插入图片到指定单元格
                                cell_address = f"{col}{excel_row}"
                                img.anchor = cell_address
                                worksheet.add_image(img)

                                self.logger.info(f"插入图片: {image_path} -> {cell_address}")

                            except Exception as e:
                                self.logger.error(f"插入图片失败: {image_path}, 错误: {e}")

            self.logger.info(f"工作表 {sheet_name} 的图片插入完成")

        except Exception as e:
            self.logger.error(f"插入图片到Excel工作表失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")

    def insert_images_to_excel_file(self, excel_file_path: str, sheet_name: str,
                                  cross_stats: list, start_date: str, end_date: str,
                                  region_name: str):
        """在已保存的Excel文件中插入图片"""
        try:
            from openpyxl import load_workbook
            from openpyxl.drawing.image import Image as OpenpyxlImage

            # 加载Excel文件
            workbook = load_workbook(excel_file_path)

            if sheet_name not in workbook.sheetnames:
                print(f"工作表 {sheet_name} 不存在")
                return

            worksheet = workbook[sheet_name]

            # 图片列的起始列号（图片1到图片6对应列H到M）
            image_columns = ['H', 'I', 'J', 'K', 'L', 'M']
            image_keys = ['image1', 'image2', 'image3', 'image4', 'image5', 'image6']

            # 设置行高和列宽以适应图片
            for col in image_columns:
                worksheet.column_dimensions[col].width = 15

            # 为每行数据插入图片
            for row_idx, item in enumerate(cross_stats):
                libparts = item.get('libparts', '')
                aoi_result = item.get('aoijudgeresult', '')
                if not libparts:
                    continue

                # 查找该libParts的所有图片（精确匹配地区和AOI检测结果）
                image_downloader = ImageDownloader()
                all_images = image_downloader.find_all_images_for_libparts(
                    libparts, start_date, end_date, region_name, aoi_result, "pics"
                )

                print(f"查找图片: libParts={libparts}, region={region_name}, aoi_result={aoi_result}, 找到{len(all_images)}张图片")

                # Excel行号（从第2行开始，因为第1行是表头）
                excel_row = row_idx + 2
                worksheet.row_dimensions[excel_row].height = 100  # 设置行高

                # 插入每张图片
                for image_key, col in zip(image_keys, image_columns):
                    if image_key in all_images:
                        image_path = all_images[image_key]
                        if os.path.exists(image_path):
                            try:
                                # 创建图片对象
                                img = OpenpyxlImage(image_path)

                                # 调整图片大小
                                img.width = 100
                                img.height = 100

                                # 插入图片到指定单元格
                                cell_address = f"{col}{excel_row}"
                                img.anchor = cell_address
                                worksheet.add_image(img)

                                print(f"插入图片: {image_path} -> {cell_address}")

                            except Exception as e:
                                print(f"插入图片失败: {image_path}, 错误: {e}")

            # 保存修改后的Excel文件
            workbook.save(excel_file_path)
            print(f"工作表 {sheet_name} 的图片插入完成，文件已保存")

        except Exception as e:
            print(f"插入图片到Excel文件失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    try:
        window = MainWindowEnhanced()
        window.show()

        sys.exit(app.exec())

    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
