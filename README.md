# AOI数据下载与统计分析系统

## 项目概述

AOI数据下载与统计分析系统是一个集成化的数据处理工具，主要用于从AOI MDS系统API获取数据、下载图片、进行数据分析和生成统计报告。该系统采用Python开发，具有图形用户界面，支持数据下载、文件管理、统计分析和报告导出等功能。

## 功能特性

### 1. 数据下载功能
- **API数据获取**：通过HTTPS协议从AOI MDS系统API获取数据
- **分页下载**：支持分页下载，可自定义每页数据量
- **时间范围控制**：支持设置开始时间和结束时间进行精确下载
- **跳跃下载**：当数据时间超出范围时，支持跳跃下载策略
- **数据存储**：按日期自动分组存储到Excel文件中

### 2. 文件管理功能
- **文件浏览**：可视化展示数据文件列表
- **文件操作**：支持删除选中文件、清理损坏文件
- **文件检查**：检查文件完整性及行数统计
- **目录管理**：支持自定义数据目录和导出目录

### 3. 统计分析功能
- **多维度统计**：按地区（南京、河源、其他）、AOI结果类型、物料代码等维度统计
- **AI覆盖分析**：分析AI算法覆盖情况和判定准确率
- **TOP N分析**：统计各类缺陷、物料代码的TOP N结果
- **交叉统计**：物料代码、AOI结果等多字段交叉统计

### 4. 报告导出功能
- **Excel导出**：支持导出详细的Excel格式统计报告
- **HTML导出**：支持导出美观的HTML格式统计报告
- **图片下载**：支持下载指定物料代码的AOI检测图片

## 项目结构

```
aoi_mds_data/
├── main.py                 # 主程序入口
├── main_window.py          # GUI主窗口实现
├── config_manager.py       # 配置管理器
├── data_download_manager.py # 数据下载管理器
├── aoi_api_client.py       # AOI API客户端
├── excel_data_manager.py   # Excel数据管理器
├── aoi_data_statistics.py  # 数据统计分析模块
├── image_downloader.py     # 图片下载模块
├── logger_setup.py         # 日志系统设置
├── requirements.txt        # 项目依赖包
├── start.bat              # Windows启动脚本
├── configs/
│   └── config.yaml         # 系统配置文件
├── data/                   # 数据文件存储目录
├── exports/                # 导出文件存储目录
├── logs/                   # 日志文件存储目录
└── pics/                   # 图片文件存储目录
```

## 核心模块说明

### 1. main.py
主程序入口文件，负责：
- 检查依赖包
- 创建必要目录
- 启动GUI应用程序

### 2. main_window.py
GUI主窗口实现，包含四个主要标签页：
- **数据下载**：控制数据下载流程
- **文件管理**：管理数据文件
- **数据统计**：执行统计分析
- **系统设置**：配置系统参数

### 3. config_manager.py
配置管理器，负责：
- 加载和保存YAML配置文件
- 管理下载、统计、时间等配置参数
- 提供配置项的获取和更新方法

### 4. data_download_manager.py
数据下载管理器，核心功能包括：
- 与AOI API客户端通信获取数据
- 实现分页下载逻辑
- 数据时间范围过滤
- 按日期分组保存到Excel文件

### 5. aoi_api_client.py
AOI API客户端，负责：
- 与AOI MDS系统API通信
- 发送POST请求获取数据
- 处理API响应和错误

### 6. excel_data_manager.py
Excel数据管理器，负责：
- 按日期分组存储数据
- 支持CSV和XLSX格式
- 实现数据追加写入功能
- 提供文件信息查询功能

### 7. aoi_data_statistics.py
数据统计分析模块，提供：
- 多维度统计分析
- AI覆盖和判定率计算
- TOP N统计分析
- 时间趋势分析

### 8. image_downloader.py
图片下载模块，支持：
- 通过SFTP协议下载AOI检测图片
- 按物料代码批量下载图片
- 图片文件管理

### 9. logger_setup.py
日志系统设置，提供：
- 按天滚动的日志文件
- 控制台和文件双重输出
- 日志文件自动清理功能

## 配置文件说明

配置文件位于 `configs/config.yaml`：

```yaml
api:
  url: https://10.50.76.198:2443/PDMDS/QualityAnalysisYield/dsapi/QualityAnalysisYield_1148708703312052224/v1
  headers:
    Content-Type: application/json
    appCode: '457202'
  payload:
    apiId: '1148708703312052224'
    empNo: '10316469'
    envType: online
    versionType: published
    hasTotal: Y
    paramMap: {}
    orderByList:
    - createTime,DESC
download:
  page_size: 1000
  max_empty_pages: 5
  file_format: xlsx
  jump_step: 1
time:
  last_update_time: '2025-08-14 00:00:00'
  default_start_time: '2025-08-01 00:00:00'
  default_stop_time: '2025-08-05 23:59:59'
  last_download_start_time: '2025-08-12 00:00:00'
  last_download_end_time: '2025-08-14 00:00:00'
paths:
  data_dir: D:/proj/27Augment/07AOI/aoi_mds_data/data
  logs_dir: logs
  export_dir: D:/proj/27Augment/07AOI/aoi_mds_data/exports
  config_dir: configs
logging:
  level: INFO
  max_days: 365
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
data:
  time_field: createTime
statistics:
  start_date: '2025-08-11'
  end_date: '2025-08-11'
  top_n: 50
```

## 安装与运行

### 环境要求
- Python 3.7+
- Windows 10 或更高版本

### 安装步骤
1. 克隆或下载项目代码
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

### 运行方式
- **Windows**: 双击 `start.bat` 文件启动程序
- **命令行**: 在项目根目录执行：
  ```bash
  python main.py
  ```

## 使用说明

### 1. 数据下载
1. 在"数据下载"标签页设置下载时间范围
2. 点击"开始下载"按钮启动下载
3. 可通过"停止下载"按钮中断下载过程
4. 下载的数据会按日期自动保存到 `data/` 目录

### 2. 文件管理
1. 在"文件管理"标签页查看数据文件列表
2. 可以删除选中的文件或清理损坏文件
3. 支持更改数据目录和导出目录

### 3. 数据统计
1. 在"数据统计"标签页设置统计时间范围和TOP N数量
2. 点击"生成统计报告"按钮开始统计
3. 统计结果会显示在各个地区标签页中
4. 可导出Excel或HTML格式的详细报告

### 4. 按钮功能详解
#### 生成统计报告按钮
点击"生成统计报告"按钮后，系统将执行以下操作：
- 验证输入的时间范围参数
- 从指定的数据目录加载指定日期范围内的Excel文件
- 对数据进行清洗，过滤测试数据和无效数据
- 按地区（南京、河源、其他）对数据进行分类
- 生成多维度统计报告，包括：
  - 基础统计信息（总数据量、AI覆盖数、AI判定率等）
  - 各类AOI缺陷的TOP N统计
  - 物料代码的TOP N统计
  - 元件类型的TOP N统计
  - 线体的TOP N统计
  - 交叉统计分析（物料代码、AOI结果等多字段组合）
  - 时间趋势分析
- 将生成的报告结果显示在对应的地区标签页中
- 同时激活导出按钮（导出XLSX报告、导出HTML报告、下载南京图片）

#### 下载南京图片按钮
点击"下载南京图片"按钮后，系统将执行以下操作：
- 检查是否存在有效的统计报告数据
- 获取南京地区代码TOP N统计中的物料代码列表
- 连接到SFTP服务器（10.50.36.25:2222）
- 从AOI数据文件中查找对应的图片地址信息
- 根据物料代码和时间范围，从SFTP服务器批量下载AOI检测图片
- 将下载的图片保存到pics/目录中
- 为每个物料代码下载对应的M.jpg格式主图片文件

#### 导出XLSX报告按钮
点击"导出XLSX报告"按钮后，系统将执行以下操作：
- 生成Excel格式的详细统计报告
- 包含多个工作表：
  - 各地区总览统计表
  - 各类缺陷统计表
  - 代码TOP N统计表
- 按照预定义的列顺序组织数据
- 保存到exports/目录下的文件中（文件名包含统计时间范围）
- 提供导出成功的提示信息

#### 导出HTML报告按钮
点击"导出HTML报告"按钮后，系统将执行以下操作：
- 生成HTML格式的美观统计报告
- 包含完整的统计信息和表格
- 使用CSS样式美化报告外观
- 包含南京和河源地区的TOP N处理情况表格
- 为每个地区创建独立的统计部分
- 保存到exports/目录下的文件中（文件名包含统计时间范围）
- 提供导出成功的提示信息

### 4. 图片下载
1. 在统计报告生成后，可点击"下载南京图片"按钮
2. 系统会自动从SFTP服务器下载指定物料代码的AOI检测图片
3. 图片保存在 `pics/` 目录中

## 日志系统

系统采用按天滚动的日志机制，日志文件保存在 `logs/` 目录中，支持自动清理过期日志文件。

## 注意事项

1. 确保网络连接正常，特别是访问AOI API和SFTP服务器
2. 确保有足够的磁盘空间存储下载的数据和图片
3. SFTP服务器需要正确的用户名和密码才能下载图片
4. 统计分析功能需要有相应的数据文件才能正常运行

## 依赖包说明

- `PySide6`: GUI框架，用于构建图形用户界面
- `pandas`: 数据处理和分析库
- `openpyxl`: Excel文件读写支持
- `requests`: HTTP请求库，用于API调用
- `PyYAML`: YAML文件解析库，用于配置文件处理
- `urllib3`: HTTP库，用于处理SSL证书问题

## 开发与维护

该项目采用模块化设计，各功能模块职责清晰，便于后续扩展和维护。如需添加新功能，建议遵循现有架构模式进行开发。
