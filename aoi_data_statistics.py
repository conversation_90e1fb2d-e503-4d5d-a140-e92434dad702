"""
AOI数据统计分析模块
基于xlsxs文件夹中的Excel文件进行数据统计分析
"""

import pandas as pd
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import glob
from logger_setup import LoggerMixin


class AOIDataStatistics(LoggerMixin):
    """AOI数据统计分析类"""

    def __init__(self, data_dir: str = "xlsxs", config_manager=None):
        """
        初始化统计分析器

        Args:
            data_dir: 数据文件目录（支持CSV和XLSX）
            config_manager: 配置管理器实例
        """
        self.data_dir = data_dir
        self.config_manager = config_manager
        
        # 字段映射（从新字段名到原字段名）
        self.field_mapping = {
            'aoiJudgeResult': 'aoiJudgeResult',      # AOI检测结果
            'libname': 'libname',                    # 元件类型
            'libParts': 'libParts',                  # 物料代码
            'componentName': 'componentName',        # 检测位号
            'aiAlgorithmName': 'aiAlgorithmName',    # AI算法名称
            'aiJudgeResult': 'aiJudgeResult',        # AI判定结果
            'lineName': 'lineName',                  # 线体
            'createTime': 'createTime'               # 创建时间
        }
        
        # AOI结果类型列表
        self.aoi_result_types = [
            '极性相反', '缺件', '错件', '偏移', '露铜', '元件翘起', 
            '少锡', '翘件', '浮高', '邻接短路', '短路', '翘脚', '翻件'
        ]
        
        # 元件类型列表
        self.component_types = [
            'CHIP-R', 'CHIP-C', 'XL', 'ALC', 'INDUCTOR', 'SON', 
            'QFN', 'MTR', 'CHIP-L', 'SOP08', 'QFP48', 'SOP16', 'SW'
        ]
    
    def load_data_by_date_range(self, start_date: str, end_date: str) -> pd.DataFrame:
        """
        根据日期范围加载数据
        
        Args:
            start_date: 开始日期，格式: YYYY-MM-DD
            end_date: 结束日期，格式: YYYY-MM-DD
            
        Returns:
            合并后的DataFrame
        """
        try:
            # 生成日期范围内的所有日期
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            
            date_list = []
            current_dt = start_dt
            while current_dt <= end_dt:
                date_list.append(current_dt.strftime("%Y-%m-%d"))
                current_dt += timedelta(days=1)
            
            # 查找对应的Excel文件
            dataframes = []
            loaded_files = []
            
            for date_str in date_list:
                # 检查CSV和XLSX文件
                for file_format in ['csv', 'xlsx']:
                    file_path = os.path.join(self.data_dir, f"aoi_data_{date_str}.{file_format}")
                    if os.path.exists(file_path):
                        try:
                            self.logger.info(f"开始读取文件: {file_path}")
                            if file_format == 'csv':
                                df = pd.read_csv(file_path)
                            else:
                                df = pd.read_excel(file_path)
                            dataframes.append(df)
                            loaded_files.append(file_path)
                            self.logger.info(f"读取完成: {file_path}, 数据量: {len(df)}")
                            break  # 找到一个文件就跳出循环
                        except Exception as e:
                            self.logger.error(f"加载文件失败 {file_path}: {e}")
            
            if not dataframes:
                self.logger.warning(f"未找到日期范围 {start_date} 到 {end_date} 的数据文件")
                return pd.DataFrame()
            
            # 合并所有数据
            combined_df = pd.concat(dataframes, ignore_index=True)
            self.logger.info(f"成功加载 {len(loaded_files)} 个文件，总数据量: {len(combined_df)}")
            
            return combined_df
            
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            return pd.DataFrame()
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理数据，过滤测试数据

        Args:
            df: 原始数据DataFrame

        Returns:
            清理后的DataFrame
        """
        try:
            original_count = len(df)

            # 过滤条件
            filters = []

            # 1. 只统计mode为生产的数据
            if 'mode' in df.columns:
                mode_filter = df['mode'] == '生产'
                filters.append(mode_filter)
                self.logger.info(f"应用mode过滤: 只保留生产数据")

            # 2. 过滤测试线体
            if 'lineName' in df.columns:
                test_line_filter = df['lineName'] != 'SMT-NJ777'
                filters.append(test_line_filter)
                self.logger.info(f"应用线体过滤: 排除SMT-NJ777")

            # 3. 过滤测试检测结果
            if 'aoiJudgeResult' in df.columns:
                test_result_filter = ~df['aoiJudgeResult'].isin([
                    '极性相反 · 测试', '极性相反 · 测', '缺件 · 测试', '缺件 · 测',
                    '错件 · 测试', '错件 · 测', '偏移 · 测试', '偏移 · 测'
                ])
                filters.append(test_result_filter)
                self.logger.info(f"应用检测结果过滤: 排除测试结果")

            # 4. 过滤屏蔽的缺陷类型
            if 'aoiJudgeResult' in df.columns and self.config_manager:
                blocked_types = self.config_manager.get_blocked_defect_types()
                if blocked_types:
                    blocked_filter = ~df['aoiJudgeResult'].isin(blocked_types)
                    filters.append(blocked_filter)
                    self.logger.info(f"应用屏蔽缺陷类型过滤: 排除 {blocked_types}")

            # 应用所有过滤条件
            if filters:
                combined_filter = filters[0]
                for f in filters[1:]:
                    combined_filter = combined_filter & f
                cleaned_df = df.loc[combined_filter]
            else:
                cleaned_df = df

            cleaned_count = len(cleaned_df)

            self.logger.info(f"数据清理完成: 原始数据 {original_count} 条，清理后 {cleaned_count} 条")

            return cleaned_df

        except Exception as e:
            self.logger.error(f"数据清理失败: {e}")
            return df

    def classify_by_region(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        按地区分类数据

        Args:
            df: 数据DataFrame

        Returns:
            按地区分类的数据字典
        """
        try:
            regions = {}

            if 'lineName' not in df.columns:
                self.logger.warning("数据中没有lineName字段，无法按地区分类")
                return {'全部': df}

            # 南京地区：SMT-NJ开头
            nj_filter = df['lineName'].str.startswith('SMT-NJ', na=False)
            regions['南京'] = df[nj_filter]

            # 河源地区：SMT-HY开头
            hy_filter = df['lineName'].str.startswith('SMT-HY', na=False)
            regions['河源'] = df[hy_filter]

            # 深圳地区：SMT-S开头
            sz_filter = df['lineName'].str.startswith('SMT-S', na=False)
            regions['深圳'] = df[sz_filter]

            # 其他地区：不是SMT-NJ、SMT-HY或SMT-S开头的
            other_filter = ~(nj_filter | hy_filter | sz_filter)
            regions['其他'] = df[other_filter]

            # 记录分类结果
            for region, region_df in regions.items():
                self.logger.info(f"{region}地区数据量: {len(region_df)}")

            return regions

        except Exception as e:
            self.logger.error(f"按地区分类失败: {e}")
            return {'全部': df}
    
    def get_basic_statistics(self, df: pd.DataFrame) -> Dict:
        """
        获取基础统计信息

        Args:
            df: 数据DataFrame

        Returns:
            统计信息字典
        """
        try:
            total_count = len(df)

            # AI算法覆盖统计：aiAlgorithmName中不是None的
            if 'aiAlgorithmName' in df.columns:
                ai_covered = df[(df['aiAlgorithmName'] != 'None') & (df['aiAlgorithmName'].notna())]
                ai_covered_count = len(ai_covered)
            else:
                ai_covered_count = 0

            # AI判定统计：aiJudgeResult是OK或NG，非None的
            if 'aiJudgeResult' in df.columns:
                ai_judged = df[(df['aiJudgeResult'].isin(['OK', 'NG'])) & (df['aiJudgeResult'].notna())]
                ai_ok = df[df['aiJudgeResult'] == 'OK']
                ai_ng = df[df['aiJudgeResult'] == 'NG']
                ai_judged_count = len(ai_judged)
                ai_ok_count = len(ai_ok)
                ai_ng_count = len(ai_ng)
            else:
                ai_judged_count = ai_ok_count = ai_ng_count = 0

            stats = {
                'total_count': total_count,
                'ai_judged_count': ai_judged_count,
                'ai_ok_count': ai_ok_count,
                'ai_ng_count': ai_ng_count,
                'ai_covered_count': ai_covered_count,
                'ai_coverage_rate': ai_covered_count / total_count * 100 if total_count > 0 else 0,
                'ai_judge_rate': ai_judged_count / total_count * 100 if total_count > 0 else 0
            }

            return stats

        except Exception as e:
            self.logger.error(f"获取基础统计信息失败: {e}")
            return {}
    
    def get_top_statistics(self, df: pd.DataFrame, top_n: int = 20,
                          stat_type: str = 'aoi_result') -> List[Dict]:
        """
        获取Top N统计，包含AI覆盖和判定信息

        Args:
            df: 数据DataFrame
            top_n: 返回前N个结果
            stat_type: 统计类型 ('aoi_result', 'material_code', 'component_type', 'line_name')

        Returns:
            统计结果列表
        """
        try:
            field_map = {
                'aoi_result': 'aoiJudgeResult',
                'material_code': 'libParts',
                'component_type': 'libname',
                'line_name': 'lineName'
            }

            if stat_type not in field_map:
                self.logger.error(f"不支持的统计类型: {stat_type}")
                return []

            field = field_map[stat_type]

            # ai_covered = df[(df['aiAlgorithmName'] != 'None') & (df['aiAlgorithmName'].notna())]
            # ai_covered_count = len(ai_covered)
            # 预处理数据：统一无效值标记
            df['aiAlgorithmName'] = df['aiAlgorithmName'].replace('None', pd.NA)

            # 按字段分组统计
            grouped = df.groupby(field).agg({
                field: 'count',  # 总数
                # 'aiAlgorithmName': lambda x: (x.astype(str) != 'None').sum() if 'aiAlgorithmName' in df.columns else 0,  # AI覆盖数
                # 'aiAlgorithmName': lambda x: (x == 'None').sum() if 'aiAlgorithmName' in df.columns else 0,  # AI覆盖数
                # 'aiAlgorithmName': lambda x: ((x != 'None') | (x is not None) | (x.astype(str) != 'None')).sum() if 'aiAlgorithmName' in df.columns else 0,  # AI覆盖数
                # 'aiAlgorithmName': ai_covered_count,  # AI覆盖数
                'aiAlgorithmName': 'count',  # AI覆盖数
                'aiJudgeResult': [
                    lambda x: ((x == 'OK') | (x == 'NG')).sum() if 'aiJudgeResult' in df.columns else 0,  # AI判定数
                    lambda x: (x == 'OK').sum() if 'aiJudgeResult' in df.columns else 0,  # AI判定OK数
                    lambda x: (x == 'NG').sum() if 'aiJudgeResult' in df.columns else 0   # AI判定NG数
                ]
            }).reset_index()

            # 重命名列
            grouped.columns = [field, 'total_count', 'ai_covered_count', 'ai_judged_count', 'ai_ok_count', 'ai_ng_count']

            # 排序并取前N个
            grouped = grouped.sort_values('total_count', ascending=False).head(top_n)

            total_count = len(df)
            results = []

            for _, row in grouped.iterrows():
                count = int(row['total_count'])
                ai_covered = int(row['ai_covered_count'])
                ai_judged = int(row['ai_judged_count'])
                ai_ok = int(row['ai_ok_count'])
                ai_ng = int(row['ai_ng_count'])

                percentage = count / total_count * 100 if total_count > 0 else 0
                ai_coverage_rate = ai_covered / count * 100 if count > 0 else 0
                ai_judge_rate = ai_judged / count * 100 if count > 0 else 0

                results.append({
                    'name': str(row[field]),
                    'count': count,
                    'percentage': round(percentage, 2),
                    'ai_covered_count': ai_covered,
                    'ai_coverage_rate': round(ai_coverage_rate, 2),
                    'ai_judged_count': ai_judged,
                    'ai_judge_rate': round(ai_judge_rate, 2),
                    'ai_ok_count': ai_ok,
                    'ai_ng_count': ai_ng
                })

            return results

        except Exception as e:
            self.logger.error(f"获取Top统计失败: {e}")
            return []
    
    def get_cross_statistics(self, df: pd.DataFrame, top_n: int = 20) -> List[Dict]:
        """
        获取交叉统计（materialCode、libParts、aoiJudgeResult组合）

        Args:
            df: 数据DataFrame
            top_n: 返回前N个结果

        Returns:
            交叉统计结果列表
        """
        try:
            # 确保必要的列存在
            required_cols = ['materialCode', 'libParts', 'aoiJudgeResult']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                self.logger.warning(f"缺少必要的列: {missing_cols}")
                # 如果缺少materialCode，使用libParts代替
                if 'materialCode' not in df.columns and 'libParts' in df.columns:
                    df = df.copy()
                    df['materialCode'] = df['libParts']

            # 按materialCode、libParts、aoiJudgeResult分组统计
            group_cols = ['materialCode', 'libParts', 'aoiJudgeResult']
            available_cols = [col for col in group_cols if col in df.columns]

            if len(available_cols) < 2:
                self.logger.error("可用的分组列不足")
                return []
            # 预处理数据：统一无效值标记
            df['aiAlgorithmName'] = df['aiAlgorithmName'].replace('None', pd.NA)
            # 分组统计
            grouped = df.groupby(available_cols).agg({
                'aoiJudgeResult': 'count',  # 总数
                'aiAlgorithmName': 'count',
                # 'aiAlgorithmName': lambda x: (x.astype(str) != 'None').sum() if 'aiAlgorithmName' in df.columns else 0,
                'aiJudgeResult': [
                    lambda x: ((x == 'OK') | (x == 'NG')).sum() if 'aiJudgeResult' in df.columns else 0,
                    lambda x: (x == 'OK').sum() if 'aiJudgeResult' in df.columns else 0,
                    lambda x: (x == 'NG').sum() if 'aiJudgeResult' in df.columns else 0
                ],
                'createTime': ['min', 'max'],  # 时间范围
                'lineName': lambda x: x.value_counts().head(3).index.tolist()  # 前3名线体
            }).reset_index()

            # 重命名列
            new_cols = available_cols + ['total_count', 'ai_covered_count', 'ai_judged_count', 'ai_ok_count', 'ai_ng_count', 'min_time', 'max_time', 'top_lines']
            grouped.columns = new_cols

            # 计算未AI判定数并按此排序
            grouped['ai_not_judged_count'] = grouped['total_count'] - grouped['ai_judged_count']

            # 按未AI判定数降序排列，然后取前N个
            grouped = grouped.sort_values('ai_not_judged_count', ascending=False).head(top_n)

            total_count = len(df)
            results = []

            for _, row in grouped.iterrows():
                count = int(row['total_count'])
                ai_covered = int(row['ai_covered_count'])
                ai_judged = int(row['ai_judged_count'])
                ai_ok = int(row['ai_ok_count'])
                ai_ng = int(row['ai_ng_count'])

                percentage = count / total_count * 100 if total_count > 0 else 0
                ai_coverage_rate = ai_covered / count * 100 if count > 0 else 0
                ai_judge_rate = ai_judged / count * 100 if count > 0 else 0

                # 时间范围
                time_range = f"{row['min_time']} ~ {row['max_time']}"

                # 前3名线体
                top_lines = row['top_lines'] if isinstance(row['top_lines'], list) else []

                # 计算未AI判定数
                ai_not_judged = count - ai_judged

                result = {
                    'count': count,
                    'percentage': round(percentage, 2),
                    'ai_judged_count': ai_judged,
                    'ai_judge_rate': round(ai_judge_rate, 2),
                    'ai_not_judged_count': ai_not_judged,
                    'ai_covered_count': ai_covered,
                    'ai_coverage_rate': round(ai_coverage_rate, 2),
                    'ai_ok_count': ai_ok,
                    'ai_ng_count': ai_ng,
                    'time_range': time_range,
                    'top_lines': top_lines
                }

                # 添加分组字段
                for col in available_cols:
                    result[col.lower()] = str(row[col])

                results.append(result)

            return results

        except Exception as e:
            self.logger.error(f"获取交叉统计失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return []
    
    def get_time_trend_statistics(self, df: pd.DataFrame) -> Dict:
        """
        获取时间趋势统计
        
        Args:
            df: 数据DataFrame
            
        Returns:
            时间趋势统计字典
        """
        try:
            # 按日期分组统计
            df['date'] = pd.to_datetime(df['createTime']).dt.date
            daily_stats = df.groupby('date').agg({
                'aoiJudgeResult': 'count',
                'aiJudgeResult': lambda x: (x != 'None').sum()
            }).reset_index()
            
            daily_stats.columns = ['date', 'total_count', 'ai_judged_count']
            daily_stats['ai_coverage_rate'] = (
                daily_stats['ai_judged_count'] / daily_stats['total_count'] * 100
            ).round(2)
            
            # 转换为字典列表
            trend_data = []
            for _, row in daily_stats.iterrows():
                trend_data.append({
                    'date': row['date'].strftime('%Y-%m-%d'),
                    'total_count': int(row['total_count']),
                    'ai_judged_count': int(row['ai_judged_count']),
                    'ai_coverage_rate': float(row['ai_coverage_rate'])
                })
            
            return {
                'daily_trend': trend_data,
                'date_range': {
                    'start': daily_stats['date'].min().strftime('%Y-%m-%d'),
                    'end': daily_stats['date'].max().strftime('%Y-%m-%d')
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取时间趋势统计失败: {e}")
            return {}
    
    def generate_comprehensive_report(self, start_date: str, end_date: str,
                                    top_n: int = 20) -> Dict:
        """
        生成综合统计报告（按地区分类）

        Args:
            start_date: 开始日期
            end_date: 结束日期
            top_n: Top N数量

        Returns:
            综合统计报告字典
        """
        try:
            self.logger.info(f"开始生成统计报告: {start_date} 到 {end_date}")

            # 加载数据
            df = self.load_data_by_date_range(start_date, end_date)
            if df.empty:
                return {'error': '未找到指定日期范围的数据'}

            # 清理数据
            cleaned_df = self.clean_data(df)

            # 按地区分类
            regions = self.classify_by_region(cleaned_df)

            # 生成分地区统计报告
            report = {
                'date_range': {'start': start_date, 'end': end_date},
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'regions': {}
            }

            for region_name, region_df in regions.items():
                if region_df.empty:
                    continue

                self.logger.info(f"生成{region_name}地区统计...")

                region_report = {
                    'basic_stats': self.get_basic_statistics(region_df),
                    'top_aoi_results': self.get_top_statistics(region_df, top_n, 'aoi_result'),
                    'top_material_codes': self.get_top_statistics(region_df, top_n, 'material_code'),
                    'top_component_types': self.get_top_statistics(region_df, top_n, 'component_type'),
                    'top_lines': self.get_top_statistics(region_df, top_n, 'line_name'),
                    'cross_statistics': self.get_cross_statistics(region_df, top_n),
                    'time_trend': self.get_time_trend_statistics(region_df)
                }

                report['regions'][region_name] = region_report

            self.logger.info("统计报告生成完成")
            return report

        except Exception as e:
            self.logger.error(f"生成统计报告失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return {'error': f'生成统计报告失败: {str(e)}'}


if __name__ == "__main__":
    """测试统计分析功能"""
    import sys
    from logger_setup import LoggerSetup
    
    # 测试配置
    test_config = {
        'paths': {'logs_dir': 'test_logs'},
        'logging': {'level': 'INFO', 'max_days': 7}
    }
    
    try:
        # 初始化日志
        logger_setup = LoggerSetup(test_config)
        
        # 创建统计分析器
        stats = AOIDataStatistics("xlsxs")
        
        # 生成测试报告
        start_date = "2025-08-05"
        end_date = "2025-08-05"
        
        report = stats.generate_comprehensive_report(start_date, end_date, top_n=10)
        
        if 'error' in report:
            print(f"✗ 统计分析测试失败: {report['error']}")
        else:
            print("✓ 统计分析测试成功")
            print(f"基础统计: 总数据量 {report['basic_stats'].get('total_count', 0)}")
            print(f"AI覆盖率: {report['basic_stats'].get('ai_coverage_rate', 0):.2f}%")
            print(f"Top AOI结果数量: {len(report['top_aoi_results'])}")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        sys.exit(1)
