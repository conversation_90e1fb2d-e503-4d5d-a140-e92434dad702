"""
AOI MDS API客户端类
用于调用AOI MDS接口获取数据
"""

import requests
import json
import urllib3
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import time

# 禁用SSL警告
urllib3.disable_warnings()


class AOIAPIClient:
    """AOI MDS API客户端"""
    
    def __init__(self, config: Dict):
        """
        初始化API客户端
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.api_url = config['api']['url']
        self.headers = config['api']['headers']
        self.base_payload = config['api']['payload']
        self.page_size = config['download']['page_size']
        self.logger = logging.getLogger(__name__)
        
    def decode_chinese_fields(self, data_rows: List[Dict]) -> List[Dict]:
        """
        解码中文字段（处理Unicode编码）
        
        Args:
            data_rows: 数据行列表
            
        Returns:
            解码后的数据行列表
        """
        decoded_rows = []
        
        for row in data_rows:
            decoded_row = {}
            for key, value in row.items():
                if isinstance(value, str):
                    try:
                        # 尝试解码可能的中文字符
                        if '\\x' in value:
                            # 处理类似 \xe7\x82\x89\xe5\x90\x8e 的编码
                            decoded_value = value.encode('latin1').decode('utf-8')
                            decoded_row[key] = decoded_value
                        else:
                            decoded_row[key] = value
                    except (UnicodeDecodeError, UnicodeEncodeError):
                        # 如果解码失败，保持原值
                        decoded_row[key] = value
                else:
                    decoded_row[key] = value
            decoded_rows.append(decoded_row)
        
        return decoded_rows
    
    def fetch_data(self, page_num: int, create_time: str = "2025-07-01 00:00:00") -> Tuple[bool, Optional[Dict]]:
        """
        获取指定页的数据

        Args:
            page_num: 页码（从0开始）
            create_time: 创建时间筛选条件，默认为2025-07-01

        Returns:
            (success, data) 元组，success表示是否成功，data包含响应数据
        """
        try:
            # 构造请求体
            # payload = self.base_payload.copy()
            # payload.update({
            #     "createTime": create_time,
            #     "pageNum": page_num,
            #     "pageSize": self.page_size
            # })
            param_map = {"createTime": create_time}
            payload = self.base_payload.copy()
            payload.update({
                "pageNum": page_num,
                "pageSize": self.page_size,
                "paramMap": param_map
            })
            
            self.logger.info(f"正在请求第{page_num}页数据，时间条件: {create_time}")
            # self.logger.info(f"headers：  {self.headers}\ndata:  {payload}")
            
            # 发送POST请求
            response = requests.post(
                url=self.api_url,
                headers=self.headers,
                data=json.dumps(payload),
                verify=False,
                timeout=30  # 30秒超时
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查业务状态码
                if result.get("code", {}).get("code") == "0000":
                    data = result["bo"]
                    rows = data["rows"]
                    columns = data["columns"]
                    total = data["total"]
                    
                    # 解码中文字段
                    decoded_rows = self.decode_chinese_fields(rows)
                    
                    response_data = {
                        "rows": decoded_rows,
                        "columns": columns,
                        "total": total,
                        "page_num": page_num,
                        "page_size": len(decoded_rows)
                    }
                    
                    self.logger.info(f"第{page_num}页请求成功，获取{len(decoded_rows)}条数据，总计: {total}条")
                    return True, response_data
                else:
                    error_msg = result["code"]["msg"]
                    self.logger.error(f"业务错误: {error_msg}")
                    return False, None
            else:
                self.logger.error(f"HTTP错误: {response.status_code}, 响应: {response.text}")
                return False, None
                
        except requests.exceptions.Timeout:
            self.logger.error(f"请求超时: 第{page_num}页")
            return False, None
        except requests.exceptions.ConnectionError:
            self.logger.error(f"连接错误: 第{page_num}页")
            return False, None
        except Exception as e:
            self.logger.error(f"请求异常: 第{page_num}页, 错误: {str(e)}")
            return False, None
    
    def get_time_range_from_data(self, rows: List[Dict]) -> Tuple[Optional[str], Optional[str]]:
        """
        从数据中获取时间范围
        
        Args:
            rows: 数据行列表
            
        Returns:
            (min_time, max_time) 元组
        """
        if not rows:
            return None, None
            
        times = []
        for row in rows:
            create_time = row.get('createTime')
            if create_time:
                times.append(create_time)
        
        if not times:
            return None, None

        times.sort()
        return times[0], times[-1]


if __name__ == "__main__":
    """
    单独运行此文件进行测试
    用法: python aoi_api_client.py [pageNum] [pageSize]
    """
    import sys
    from config_manager import ConfigManager
    from logger_setup import LoggerSetup

    logger = logging.getLogger(__name__)

    # 解析命令行参数
    # page_num = 0
    page_nums = 3
    page_size = 100

    for page_num in range(page_nums):

        if len(sys.argv) > 1:
            try:
                page_num = int(sys.argv[1])
            except ValueError:
                print("错误: pageNum 必须是整数")
                sys.exit(1)

        if len(sys.argv) > 2:
            try:
                page_size = int(sys.argv[2])
            except ValueError:
                print("错误: pageSize 必须是整数")
                sys.exit(1)

        print(f"AOI API 客户端测试")
        print(f"页码: {page_num}, 页面大小: {page_size}")
        print("=" * 50)

        try:
            # 加载配置
            config_manager = ConfigManager()
            config = config_manager.get_config()

            # 临时修改页面大小
            config['download']['page_size'] = page_size

            # 初始化日志系统
            logger_setup = LoggerSetup(config)

            # 创建API客户端
            api_client = AOIAPIClient(config)

            # 获取数据
            print(f"正在获取第 {page_num} 页数据...")
            success, data = api_client.fetch_data(page_num)

            if success:
                rows = data['rows']
                columns = data['columns']
                total = data['total']

                print(f"✓ 请求成功!")
                print(f"获取数据: {len(rows)} 条")
                print(f"总数据量: {total} 条")
                print(f"列名: {len(columns)} 个字段")
                # print(f"data: {data}")

                if rows:
                    # 获取时间范围
                    min_time, max_time = api_client.get_time_range_from_data(rows)
                    if min_time and max_time:
                        print(f"时间范围: {min_time} ~ {max_time}")

                    # 显示前3条数据示例
                    print("\n前3条数据示例:")
                    for i, row in enumerate(rows):
                        logger.info(f"page_num:{page_num}: 记录 {i+1}: 创建时间: {row.get('createTime')}")

                    # # 显示前3条数据示例
                    # print("\n前3条数据示例:")
                    # for i, row in enumerate(rows[:3]):
                    #     print(f"记录 {i+1}:")
                    #     print(f"  ID: {row.get('id')}")
                    #     print(f"  程序名: {row.get('programName')}")
                    #     print(f"  创建时间: {row.get('createTime')}")
                    #     print(f"  AOI结果: {row.get('aoiJudgeResult')}")
                    #     print()
                else:
                    print("该页没有数据")
            else:
                print("✗ 请求失败")

        except Exception as e:
            print(f"✗ 运行出错: {e}")
            import traceback
            traceback.print_exc()
