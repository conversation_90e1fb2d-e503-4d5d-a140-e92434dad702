"""
配置管理器
用于管理YAML配置文件的读取和更新
"""

import yaml
import os
from datetime import datetime
from typing import Dict, Any
import logging


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_path: str = "configs/config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        self._config = None
        
        # 确保配置目录存在
        config_dir = os.path.dirname(config_path)
        if config_dir and not os.path.exists(config_dir):
            os.makedirs(config_dir)
            self.logger.info(f"创建配置目录: {config_dir}")
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        try:
            if not os.path.exists(self.config_path):
                self.logger.error(f"配置文件不存在: {self.config_path}")
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self._config = yaml.safe_load(file)
                self.logger.info(f"成功加载配置文件: {self.config_path}")
                return self._config
                
        except yaml.YAMLError as e:
            self.logger.error(f"YAML解析错误: {e}")
            raise
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            raise
    
    def save_config(self, config: Dict[str, Any] = None) -> bool:
        """
        保存配置文件
        
        Args:
            config: 要保存的配置字典，如果为None则保存当前配置
            
        Returns:
            是否保存成功
        """
        try:
            config_to_save = config if config is not None else self._config
            if config_to_save is None:
                self.logger.error("没有配置数据可保存")
                return False
            
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(config_to_save, file, default_flow_style=False, 
                         allow_unicode=True, sort_keys=False)
                self.logger.info(f"成功保存配置文件: {self.config_path}")
                return True
                
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取配置
        
        Returns:
            配置字典
        """
        if self._config is None:
            self._config = self.load_config()
        return self._config
    
    def update_last_update_time(self, time_str: str) -> bool:
        """
        更新最后更新时间
        
        Args:
            time_str: 时间字符串，格式: YYYY-MM-DD HH:MM:SS
            
        Returns:
            是否更新成功
        """
        try:
            # 验证时间格式
            datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
            
            if self._config is None:
                self._config = self.load_config()
            
            self._config['time']['last_update_time'] = time_str
            success = self.save_config()
            
            if success:
                self.logger.info(f"更新最后更新时间: {time_str}")
            
            return success
            
        except ValueError as e:
            self.logger.error(f"时间格式错误: {time_str}, 错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"更新最后更新时间失败: {e}")
            return False
    
    def get_last_update_time(self) -> str:
        """
        获取最后更新时间
        
        Returns:
            最后更新时间字符串
        """
        config = self.get_config()
        return config.get('time', {}).get('last_update_time', '1970-01-01 00:00:00')
    
    def get_default_start_time(self) -> str:
        """
        获取默认开始时间

        Returns:
            默认开始时间字符串
        """
        config = self.get_config()
        return config.get('time', {}).get('default_start_time', '2025-08-01 00:00:00')

    def get_last_download_start_time(self) -> str:
        """
        获取上次下载开始时间

        Returns:
            上次下载开始时间字符串
        """
        config = self.get_config()
        return config.get('time', {}).get('last_download_start_time', '2025-08-01 00:00:00')

    def get_last_download_end_time(self) -> str:
        """
        获取上次下载结束时间

        Returns:
            上次下载结束时间字符串
        """
        config = self.get_config()
        return config.get('time', {}).get('last_download_end_time', '2025-08-01 23:59:59')

    def update_last_download_times(self, start_time: str, end_time: str) -> bool:
        """
        更新上次下载时间范围

        Args:
            start_time: 下载开始时间
            end_time: 下载结束时间

        Returns:
            是否更新成功
        """
        try:
            config = self.get_config()

            if 'time' not in config:
                config['time'] = {}

            config['time']['last_download_start_time'] = start_time
            config['time']['last_download_end_time'] = end_time
            # 保持兼容性
            config['time']['last_update_time'] = end_time

            return self.save_config(config)

        except Exception as e:
            self.logger.error(f"更新下载时间范围失败: {e}")
            return False

    def get_stats_start_date(self) -> str:
        """
        获取统计开始日期

        Returns:
            统计开始日期字符串
        """
        config = self.get_config()
        return config.get('statistics', {}).get('start_date', '2025-08-01')

    def get_stats_end_date(self) -> str:
        """
        获取统计结束日期

        Returns:
            统计结束日期字符串
        """
        config = self.get_config()
        return config.get('statistics', {}).get('end_date', '2025-08-07')

    def get_stats_top_n(self) -> int:
        """
        获取统计Top N数量

        Returns:
            Top N数量
        """
        config = self.get_config()
        return config.get('statistics', {}).get('top_n', 20)

    def update_stats_params(self, start_date: str, end_date: str, top_n: int) -> bool:
        """
        更新统计参数

        Args:
            start_date: 统计开始日期
            end_date: 统计结束日期
            top_n: Top N数量

        Returns:
            是否更新成功
        """
        try:
            config = self.get_config()

            if 'statistics' not in config:
                config['statistics'] = {}

            config['statistics']['start_date'] = start_date
            config['statistics']['end_date'] = end_date
            config['statistics']['top_n'] = top_n

            return self.save_config(config)

        except Exception as e:
            self.logger.error(f"更新统计参数失败: {e}")
            return False

    def get_jump_step(self) -> int:
        """
        获取跳跃步长

        Returns:
            跳跃步长
        """
        config = self.get_config()
        return config.get('download', {}).get('jump_step', 5)

    def update_jump_step(self, jump_step: int) -> bool:
        """
        更新跳跃步长

        Args:
            jump_step: 跳跃步长

        Returns:
            是否更新成功
        """
        try:
            config = self.get_config()

            if 'download' not in config:
                config['download'] = {}

            config['download']['jump_step'] = jump_step

            return self.save_config(config)

        except Exception as e:
            self.logger.error(f"更新跳跃步长失败: {e}")
            return False
    
    def get_page_size(self) -> int:
        """
        获取页面大小
        
        Returns:
            页面大小
        """
        config = self.get_config()
        return config.get('download', {}).get('page_size', 1000)
    
    def get_max_empty_pages(self) -> int:
        """
        获取最大空页数
        
        Returns:
            最大空页数
        """
        config = self.get_config()
        return config.get('download', {}).get('max_empty_pages', 5)
    
    def get_data_dir(self) -> str:
        """
        获取数据文件目录

        Returns:
            数据文件目录路径
        """
        config = self.get_config()
        return config.get('paths', {}).get('data_dir', 'xlsxs')

    def get_export_dir(self) -> str:
        """
        获取导出目录

        Returns:
            导出目录路径
        """
        config = self.get_config()
        return config.get('paths', {}).get('export_dir', 'exports')

    def get_file_format(self) -> str:
        """
        获取文件格式（强制返回xlsx）

        Returns:
            文件格式 (强制xlsx)
        """
        return 'xlsx'  # 强制使用xlsx格式

    def get_default_stop_time(self) -> str:
        """
        获取默认停止时间

        Returns:
            默认停止时间字符串
        """
        config = self.get_config()
        return config.get('time', {}).get('default_stop_time', '2025-08-05 23:59:59')

    def update_data_dir(self, data_dir: str) -> bool:
        """
        更新数据目录

        Args:
            data_dir: 新的数据目录路径

        Returns:
            是否更新成功
        """
        try:
            if self._config is None:
                self._config = self.load_config()

            if 'paths' not in self._config:
                self._config['paths'] = {}

            self._config['paths']['data_dir'] = data_dir
            success = self.save_config()

            if success:
                self.logger.info(f"更新数据目录: {data_dir}")

            return success

        except Exception as e:
            self.logger.error(f"更新数据目录失败: {e}")
            return False

    def update_export_dir(self, export_dir: str) -> bool:
        """
        更新导出目录

        Args:
            export_dir: 新的导出目录路径

        Returns:
            是否更新成功
        """
        try:
            if self._config is None:
                self._config = self.load_config()

            if 'paths' not in self._config:
                self._config['paths'] = {}

            self._config['paths']['export_dir'] = export_dir
            success = self.save_config()

            if success:
                self.logger.info(f"更新导出目录: {export_dir}")

            return success

        except Exception as e:
            self.logger.error(f"更新导出目录失败: {e}")
            return False

    def update_file_format(self, file_format: str) -> bool:
        """
        更新文件格式

        Args:
            file_format: 新的文件格式 (csv 或 xlsx)

        Returns:
            是否更新成功
        """
        try:
            if file_format not in ['csv', 'xlsx']:
                self.logger.error(f"不支持的文件格式: {file_format}")
                return False

            if self._config is None:
                self._config = self.load_config()

            if 'download' not in self._config:
                self._config['download'] = {}

            self._config['download']['file_format'] = file_format
            success = self.save_config()

            if success:
                self.logger.info(f"更新文件格式: {file_format}")

            return success

        except Exception as e:
            self.logger.error(f"更新文件格式失败: {e}")
            return False
    
    def get_logs_dir(self) -> str:
        """
        获取日志目录
        
        Returns:
            日志目录路径
        """
        config = self.get_config()
        return config.get('paths', {}).get('logs_dir', 'logs')


if __name__ == "__main__":
    """测试配置管理器"""
    import sys
    
    # 设置基本日志
    logging.basicConfig(level=logging.INFO, 
                       format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 加载配置
        config = config_manager.get_config()
        print("配置加载成功:")
        print(f"  API URL: {config['api']['url']}")
        print(f"  页面大小: {config_manager.get_page_size()}")
        print(f"  最后更新时间: {config_manager.get_last_update_time()}")
        print(f"  Excel目录: {config_manager.get_xlsx_dir()}")
        print(f"  日志目录: {config_manager.get_logs_dir()}")
        
        # 测试更新最后更新时间
        test_time = "2025-08-05 12:00:00"
        if config_manager.update_last_update_time(test_time):
            print(f"✓ 成功更新最后更新时间: {test_time}")
        else:
            print("✗ 更新最后更新时间失败")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        sys.exit(1)
