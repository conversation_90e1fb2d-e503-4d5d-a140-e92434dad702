#!/usr/bin/env python3
"""
自动并行数据导出脚本 - 无需交互
"""

import sys
import os
from pathlib import Path
import pandas as pd
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from aoi_api_client import AOIAPIClient
from config_manager import ConfigManager

class ParallelDataExporter:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.config = self.config_manager.get_config()
        self.api_client = AOIAPIClient(self.config)
        self.page_size = self.config['download']['page_size']
        self.start_time = "2025-08-18 00:00:00"
        self.end_time = "2025-08-18 23:59:59"
        self.all_data_lock = threading.Lock()
        
    def fetch_single_page(self, page_num):
        """获取单页数据"""
        try:
            # print(f"[线程 {threading.current_thread().name}] 正在获取第 {page_num + 1} 页数据...")
            success, result = self.api_client.fetch_data(page_num, self.start_time)
            
            if success and result:
                records = result['rows']
                # print(f"[线程 {threading.current_thread().name}] 成功获取第 {page_num + 1} 页 {len(records)} 条记录")
                return {
                    'page_num': page_num,
                    'records': records,
                    'success': True
                }
            else:
                # print(f"[线程 {threading.current_thread().name}] 获取第 {page_num + 1} 页失败")
                return {
                    'page_num': page_num,
                    'records': [],
                    'success': False
                }
        except Exception as e:
            # print(f"[线程 {threading.current_thread().name}] 获取第 {page_num + 1} 页时出错: {e}")
            return {
                'page_num': page_num,
                'records': [],
                'success': False
            }
    
    def export_all_data_parallel(self, max_pages=500):
        """使用20个线程并行导出所有数据"""
        print("=" * 60)
        print("并行数据导出启动")
        print(f"页面大小: {self.page_size} 条记录")
        print(f"最大页数: {max_pages} 页")
        print("使用 20 个线程并行获取数据...")
        print("=" * 60)
        
        all_data = []
        
        # 使用线程池并行获取数据
        with ThreadPoolExecutor(max_workers=20) as executor:
            # 提交所有任务
            futures = []
            for page_num in range(max_pages):
                future = executor.submit(self.fetch_single_page, page_num)
                futures.append(future)
            
            # 收集结果
            completed = 0
            for future in as_completed(futures):
                result = future.result()
                if result['success'] and result['records']:
                    all_data.extend(result['records'])
                    completed += 1
                    print(f"已收集第 {result['page_num'] + 1} 页数据，累计: {len(all_data)} 条记录")
                    
                    # 每获取10页就显示进度
                    if completed % 10 == 0:
                        print(f"进度: 已完成 {completed} 页，共 {len(all_data)} 条记录")
                        
        print("=" * 60)
        if all_data:
            print(f"🎉 并行获取完成！总共获取到 {len(all_data)} 条记录")
            
            # 保存到Excel文件
            print("正在保存到 data.xlsx 文件...")
            try:
                df = pd.DataFrame(all_data)
                df.to_excel("data.xlsx", index=False)
                print("✅ 数据已成功保存到 data.xlsx")
                print(f"📊 文件包含 {len(df)} 条记录")
                print("📁 文件保存位置: 当前目录")
                
                # 显示文件统计信息
                print("\n📈 文件统计信息:")
                print(f"   - 记录总数: {len(df)}")
                print(f"   - 列数: {len(df.columns)}")
                print(f"   - 内存占用: {df.memory_usage(deep=True).sum() / 1024:.2f} KB")
                
                return True
            except Exception as e:
                print(f"❌ 保存数据到 Excel 文件失败: {e}")
                return False
        else:
            print("⚠️ 未获取到任何数据")
            return False

def main():
    """主函数 - 自动运行"""
    print("自动并行数据导出工具启动...")
    print("无需交互，直接开始执行...")
    
    exporter = ParallelDataExporter()
    success = exporter.export_all_data_parallel(max_pages=500)  # 只获取前50页进行测试
    
    if success:
        print("\n🎉 并行导出完成！")
        print("文件已保存为 data.xlsx")
    else:
        print("\n❌ 并行导出失败！")

if __name__ == "__main__":
    main()