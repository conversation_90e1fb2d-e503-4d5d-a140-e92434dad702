"""
数据存储管理器
按createTime日期分组存储数据到不同的csv或xlsx文件，支持追加写入
"""

import pandas as pd
import os
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import logging
from logger_setup import LoggerMixin
import csv
from openpyxl import load_workbook


class DataFileManager(LoggerMixin):
    """数据文件存储管理器（支持CSV和XLSX）"""

    def __init__(self, data_dir: str = "xlsxs", file_format: str = "xlsx"):
        """
        初始化数据文件管理器

        Args:
            data_dir: 数据文件存储目录
            file_format: 文件格式，支持csv和xlsx
        """
        self.data_dir = data_dir
        # 支持csv和xlsx两种格式，默认使用xlsx
        self.file_format = file_format.lower() if file_format.lower() in ['csv', 'xlsx'] else 'csv'

        # 确保目录存在
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            self.logger.info(f"创建数据存储目录: {self.data_dir}")

    def set_file_format(self, file_format: str):
        """
        设置文件格式

        Args:
            file_format: 文件格式 ('csv' 或 'xlsx')
        """
        if file_format.lower() in ['csv', 'xlsx']:
            self.file_format = file_format.lower()
            self.logger.info(f"文件格式已设置为: {self.file_format}")
        else:
            self.logger.warning(f"不支持的文件格式: {file_format}，保持当前格式: {self.file_format}")
    
    def extract_date_from_time(self, time_str: str) -> Optional[str]:
        """
        从时间字符串中提取日期
        
        Args:
            time_str: 时间字符串，格式: YYYY-MM-DD HH:MM:SS
            
        Returns:
            日期字符串，格式: YYYY-MM-DD，如果解析失败返回None
        """
        try:
            if not time_str:
                return None
            
            # 尝试解析时间字符串
            dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
            return dt.strftime("%Y-%m-%d")
            
        except ValueError:
            self.logger.warning(f"无法解析时间字符串: {time_str}")
            return None
    
    def get_data_file_path(self, date_str: str) -> str:
        """
        获取指定日期的数据文件路径

        Args:
            date_str: 日期字符串，格式: YYYY-MM-DD

        Returns:
            数据文件完整路径
        """
        filename = f"aoi_data_{date_str}.{self.file_format}"
        return os.path.join(self.data_dir, filename)
    
    def group_data_by_date(self, data_rows: List[Dict[str, Any]], 
                          time_field: str = "createTime") -> Dict[str, List[Dict[str, Any]]]:
        """
        按日期分组数据
        
        Args:
            data_rows: 数据行列表
            time_field: 时间字段名
            
        Returns:
            按日期分组的数据字典，键为日期字符串，值为该日期的数据列表
        """
        grouped_data = {}
        
        for row in data_rows:
            time_str = row.get(time_field)
            if not time_str:
                self.logger.warning(f"数据行缺少时间字段 {time_field}: {row}")
                continue
            
            date_str = self.extract_date_from_time(time_str)
            if not date_str:
                continue
            
            if date_str not in grouped_data:
                grouped_data[date_str] = []
            
            grouped_data[date_str].append(row)
        
        return grouped_data
    
    def save_data_to_file(self, data_rows: List[Dict[str, Any]],
                         date_str: str, append: bool = True) -> Tuple[bool, int]:
        """
        保存数据到文件（CSV或XLSX）

        Args:
            data_rows: 要保存的数据行列表
            date_str: 日期字符串
            append: 是否追加模式，True为追加，False为覆盖

        Returns:
            (success, saved_count) 元组，success表示是否成功，saved_count表示保存的行数
        """
        try:
            if not data_rows:
                self.logger.warning("没有数据需要保存")
                return True, 0

            data_file = self.get_data_file_path(date_str)
            df_new = pd.DataFrame(data_rows)

            if os.path.exists(data_file):
                # 追加模式：使用openpyxl增量写入
                try:
                    if self.file_format == 'xlsx':
                        # 加载现有Excel文件
                        wb = load_workbook(data_file)
                        ws = wb.active

                        # 增量追加数据（不读取整个文件内容，只追加新行）
                        for _, row in df_new.iterrows():
                            # 处理空值（保持与原有逻辑一致）
                            row_data = [
                                'None' if pd.isna(value) else value
                                for value in row.values
                            ]
                            ws.append(row_data)

                        # 保存文件
                        wb.save(data_file)
                        wb.close()  # 确保文件正确关闭
                    elif self.file_format == 'csv':
                        # self.logger.info(f"data_rows:      {data_rows}")
                        with open(data_file, 'a', newline='', encoding="utf-8-sig") as file:
                            writer = csv.writer(file)
                            # 处理空值 + 提取字典值
                            processed_rows = []
                            for row in data_rows:
                                processed_row = []
                                for value in row.values():
                                    # 空值处理
                                    if value is None or pd.isna(value):
                                        clean_value = 'None'
                                    else:
                                        clean_value = value
                                    # 强制转字符串
                                    processed_row.append(str(clean_value))  # 显式转换
                                processed_rows.append(processed_row)
                            
                            # 批量写入所有行
                            writer.writerows(processed_rows)

                    saved_count = len(df_new)

                    self.logger.info(f"增量追加 {saved_count} 条数据到 {data_file}")

                except Exception as e:
                    self.logger.error(f"增量追加失败: {e}，将创建新文件")
                    if self.file_format == 'xlsx':
                        df_new.to_excel(data_file, index=False)
                    elif self.file_format == 'csv':
                        df_new.to_csv(data_file, index=False, encoding='utf-8-sig')
                    saved_count = len(df_new)
                    self.logger.info(f"创建新文件并保存 {saved_count} 条数据到 {data_file}")
            else:
                # 覆盖模式或文件不存在：直接保存
                if self.file_format == 'xlsx':
                    df_new.to_excel(data_file, index=False)
                elif self.file_format == 'csv':
                    df_new.to_csv(data_file, index=False, encoding='utf-8-sig')
                saved_count = len(df_new)
                self.logger.info(f"保存 {saved_count} 条数据到 {data_file}")

            return True, saved_count

        except Exception as e:
            self.logger.error(f"保存数据到文件失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False, 0

    def verify_incremental_write(self, data_file: str, expected_new_rows: int) -> bool:
        """
        验证增量写入是否成功

        Args:
            data_file: 数据文件路径
            expected_new_rows: 预期新增的行数

        Returns:
            验证是否成功
        """
        try:
            if not os.path.exists(data_file):
                self.logger.warning(f"文件不存在，无法验证: {data_file}")
                return False

            if self.file_format == 'csv':
                with open(data_file, 'r', encoding='utf-8-sig') as f:
                    row_count = sum(1 for _ in f) - 1  # 减去表头行
            elif self.file_format == 'xlsx':
                from openpyxl import load_workbook
                wb = load_workbook(data_file)
                ws = wb.active
                row_count = ws.max_row - 1  # 减去表头行
                wb.close()
            else:
                return False

            self.logger.info(f"文件 {data_file} 当前数据行数: {row_count}")
            return True

        except Exception as e:
            self.logger.error(f"验证增量写入失败: {e}")
            return False
    
    def save_grouped_data(self, data_rows: List[Dict[str, Any]], 
                         time_field: str = "createTime") -> Tuple[bool, Dict[str, int]]:
        """
        按日期分组并保存数据
        
        Args:
            data_rows: 数据行列表
            time_field: 时间字段名
            
        Returns:
            (success, save_summary) 元组，success表示是否全部成功，
            save_summary为保存摘要字典，键为日期，值为保存的行数
        """
        try:
            # 按日期分组数据
            grouped_data = self.group_data_by_date(data_rows, time_field)
            
            if not grouped_data:
                self.logger.warning("没有有效数据需要保存")
                return True, {}
            
            save_summary = {}
            all_success = True
            
            # 按日期排序（降序）
            sorted_dates = sorted(grouped_data.keys(), reverse=True)
            
            for date_str in sorted_dates:
                date_data = grouped_data[date_str]
                success, saved_count = self.save_data_to_file(date_data, date_str, append=True)
                
                save_summary[date_str] = saved_count
                
                if not success:
                    all_success = False
                    self.logger.error(f"保存日期 {date_str} 的数据失败")
                else:
                    self.logger.info(f"成功保存日期 {date_str} 的 {saved_count} 条数据")
            
            return all_success, save_summary
            
        except Exception as e:
            self.logger.error(f"分组保存数据失败: {e}")
            return False, {}
    
    def get_existing_files(self) -> List[str]:
        """
        获取现有的数据文件列表（CSV和XLSX）

        Returns:
            数据文件路径列表
        """
        try:
            if not os.path.exists(self.data_dir):
                return []

            files = []
            for filename in os.listdir(self.data_dir):
                if (filename.endswith('.csv') or filename.endswith('.xlsx')) and filename.startswith('aoi_data_'):
                    files.append(os.path.join(self.data_dir, filename))

            return sorted(files)

        except Exception as e:
            self.logger.error(f"获取数据文件列表失败: {e}")
            return []

    def delete_file(self, file_path: str) -> bool:
        """
        删除指定的数据文件

        Args:
            file_path: 要删除的文件路径

        Returns:
            是否删除成功
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                self.logger.info(f"删除文件: {file_path}")
                return True
            else:
                self.logger.warning(f"文件不存在: {file_path}")
                return False

        except Exception as e:
            self.logger.error(f"删除文件失败 {file_path}: {e}")
            return False
    
    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        获取数据文件信息

        Args:
            file_path: 数据文件路径

        Returns:
            文件信息字典，包含行数、日期等信息
        """
        try:
            if not os.path.exists(file_path):
                return None

            filename = os.path.basename(file_path)
            file_ext = os.path.splitext(filename)[1].lower()

            # 从文件名提取日期
            date_part = filename.replace('aoi_data_', '').replace(file_ext, '')

            # 基本文件信息
            file_info = {
                'file_path': file_path,
                'filename': filename,
                'date': date_part,
                'format': file_ext[1:],  # 去掉点号
                'file_size': os.path.getsize(file_path),
                'modified_time': datetime.fromtimestamp(os.path.getmtime(file_path)),
                'row_count': 0,  # 默认值
                'status': 'unknown'  # 文件状态
            }

            # 尝试读取文件获取行数
            try:
                if file_ext == '.csv':
                    df = pd.read_csv(file_path)
                    file_info['row_count'] = len(df)
                    file_info['status'] = 'ok'
                elif file_ext == '.xlsx':
                    df = pd.read_excel(file_path)
                    file_info['row_count'] = len(df)
                    file_info['status'] = 'ok'
                else:
                    file_info['status'] = 'unsupported'
                    self.logger.warning(f"不支持的文件格式: {file_path}")

            except Exception as read_error:
                # 文件读取失败，但仍返回基本信息
                file_info['status'] = 'corrupted'
                self.logger.warning(f"文件可能损坏，无法读取内容 {file_path}: {read_error}")

            return file_info

        except Exception as e:
            self.logger.error(f"获取文件信息失败 {file_path}: {e}")
            # 返回基本信息，即使出错
            try:
                filename = os.path.basename(file_path)
                file_ext = os.path.splitext(filename)[1].lower()
                date_part = filename.replace('aoi_data_', '').replace(file_ext, '')

                return {
                    'file_path': file_path,
                    'filename': filename,
                    'date': date_part,
                    'format': file_ext[1:],
                    'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                    'modified_time': datetime.fromtimestamp(os.path.getmtime(file_path)) if os.path.exists(file_path) else datetime.now(),
                    'row_count': 0,
                    'status': 'error'
                }
            except:
                return None


if __name__ == "__main__":
    """测试数据文件管理器"""
    import sys
    from logger_setup import LoggerSetup

    # 测试配置
    test_config = {
        'paths': {'logs_dir': 'test_logs'},
        'logging': {'level': 'INFO', 'max_days': 7}
    }

    try:
        # 初始化日志
        logger_setup = LoggerSetup(test_config)

        # 创建测试数据
        test_data = [
            {'id': 1, 'createTime': '2025-08-05 10:00:00', 'data': 'test1'},
            {'id': 2, 'createTime': '2025-08-05 11:00:00', 'data': 'test2'},
            {'id': 3, 'createTime': '2025-08-04 10:00:00', 'data': 'test3'},
            {'id': 4, 'createTime': '2025-08-04 11:00:00', 'data': 'test4'},
        ]

        # 测试CSV格式
        print("测试CSV格式...")
        csv_manager = DataFileManager("test_data_csv", "csv")
        success, summary = csv_manager.save_grouped_data(test_data)

        if success:
            print("✓ CSV数据管理器测试成功")
            print(f"保存摘要: {summary}")

        # 测试XLSX格式
        print("测试XLSX格式...")
        xlsx_manager = DataFileManager("test_data_xlsx", "xlsx")
        success, summary = xlsx_manager.save_grouped_data(test_data)

        if success:
            print("✓ XLSX数据管理器测试成功")
            print(f"保存摘要: {summary}")

            # 测试获取文件信息
            files = xlsx_manager.get_existing_files()
            for file_path in files:
                info = xlsx_manager.get_file_info(file_path)
                if info:
                    print(f"文件: {info['filename']}, 格式: {info['format']}, 行数: {info['row_count']}")

        # 清理测试文件
        import shutil
        for dir_name in ['test_data_csv', 'test_data_xlsx', 'test_logs']:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)

        print("✓ 所有测试完成")

    except Exception as e:
        print(f"✗ 测试失败: {e}")
        sys.exit(1)
