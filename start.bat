@echo off
echo AOI数据下载器启动脚本
echo ========================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查虚拟环境
if exist ".venv\Scripts\activate.bat" (
    echo 激活虚拟环境...
    call .venv\Scripts\activate.bat
) else (
    echo 警告: 未找到虚拟环境，使用系统Python
)

REM 安装依赖
echo 检查并安装依赖包...
pip install -r requirements.txt

REM 启动程序
echo 启动AOI数据下载器...
python main.py

pause
