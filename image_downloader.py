"""
图片下载模块
用于下载AOI数据中的图片文件
"""

import os
import json
import paramiko
import pandas as pd
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from logger_setup import LoggerSetup

class ImageDownloader:
    """图片下载器"""
    
    # 不同地区的SFTP连接配置
    SFTP_CONFIGS = {
        '南京': {
            'host': '***********',
            'port': 2222,
            'username': 'testfileupload',
            'password': 'tf@4490Up#0714'
        },
        '河源': {
            'host': '************',
            'port': 2222,
            'username': 'testfileupload',
            'password': 'TestUl#97.43'
        }
    }

    def __init__(self):
        """初始化图片下载器"""
        from config_manager import ConfigManager
        config_manager = ConfigManager()
        config_dict = config_manager.get_config()
        self.logger = LoggerSetup(config_dict).get_logger()
        self.sftp = None
        self.ssh = None
        self.current_region = None  # 当前连接的地区
    
    def connect_sftp(self, region: str = '南京') -> bool:
        """连接SFTP服务器"""
        try:
            # 如果已经连接到相同地区，直接返回
            if self.sftp and self.current_region == region:
                return True

            # 断开现有连接
            if self.sftp:
                self.disconnect_sftp()

            # 获取地区配置
            if region not in self.SFTP_CONFIGS:
                self.logger.error(f"不支持的地区: {region}")
                return False

            config = self.SFTP_CONFIGS[region]

            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh.connect(
                hostname=config['host'],
                port=config['port'],
                username=config['username'],
                password=config['password']
            )
            self.sftp = self.ssh.open_sftp()
            self.current_region = region
            self.logger.info(f"SFTP连接成功 - {region}地区 ({config['host']})")
            return True

        except Exception as e:
            self.logger.error(f"SFTP连接失败 - {region}地区: {e}")
            return False
    
    def disconnect_sftp(self):
        """断开SFTP连接"""
        try:
            if self.sftp:
                self.sftp.close()
                self.sftp = None
            if self.ssh:
                self.ssh.close()
                self.ssh = None
            self.current_region = None
            self.logger.info("SFTP连接已断开")
        except Exception as e:
            self.logger.error(f"断开SFTP连接失败: {e}")
    
    def parse_image_address(self, image_address: str) -> Dict[str, str]:
        """解析imageResultAddress字段"""
        try:
            # 尝试解析JSON格式
            if image_address.startswith('{') and image_address.endswith('}'):
                return json.loads(image_address)
            else:
                # 如果不是JSON格式，尝试其他解析方式
                self.logger.warning(f"图片地址格式不是标准JSON: {image_address}")
                return {}
        except Exception as e:
            self.logger.error(f"解析图片地址失败: {e}, 地址: {image_address}")
            return {}
    
    def get_latest_image_address_for_libparts(self, data_file: str, libparts: str,
                                            region: str = "", aoi_result: str = "") -> Dict[str, str]:
        """获取指定libParts、地区和AOI检测结果的最新图片地址"""
        try:
            # 读取Excel文件
            df = pd.read_excel(data_file)

            required_columns = ['libParts', 'imageResultAddress']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                self.logger.error(f"Excel文件缺少必要的列: {missing_columns}")
                return {}

            # 筛选指定libParts的数据
            filtered_df = df[df['libParts'] == libparts]

            # 如果提供了地区信息，进一步筛选
            if region and 'lineName' in df.columns:
                # 根据线体名称判断地区（南京线体包含NJ，河源线体包含HY等）
                if region == "南京":
                    filtered_df = filtered_df[filtered_df['lineName'].str.contains('NJ', na=False)]
                elif region == "河源":
                    filtered_df = filtered_df[filtered_df['lineName'].str.contains('HY', na=False)]

            # 如果提供了AOI检测结果，进一步筛选
            if aoi_result and 'aoiJudgeResult' in df.columns:
                filtered_df = filtered_df[filtered_df['aoiJudgeResult'] == aoi_result]

            if filtered_df.empty:
                self.logger.warning(f"未找到匹配条件的数据: libParts={libparts}, region={region}, aoi_result={aoi_result}")
                return {}

            # 按时间排序，获取最新的记录
            if 'createTime' in filtered_df.columns:
                filtered_df = filtered_df.sort_values('createTime', ascending=False)

            # 获取最新记录的图片地址
            latest_record = filtered_df.iloc[0]
            image_address = latest_record['imageResultAddress']

            self.logger.info(f"找到匹配记录: libParts={libparts}, region={region}, aoi_result={aoi_result}, 时间={latest_record.get('createTime', 'N/A')}")

            return self.parse_image_address(str(image_address))

        except Exception as e:
            self.logger.error(f"获取图片地址失败: libParts={libparts}, region={region}, aoi_result={aoi_result}, 错误={e}")
            return {}
    
    def download_single_image(self, remote_path: str, local_path: str) -> bool:
        """下载单张图片"""
        try:
            if not self.sftp:
                self.logger.error("SFTP未连接")
                return False
            
            # 确保本地目录存在
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # 下载文件
            self.sftp.get(remote_path, local_path)
            self.logger.info(f"图片下载成功: {remote_path} -> {local_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"下载图片失败: {remote_path} -> {local_path}, 错误: {e}")
            return False
    
    def download_images_for_libparts(self, data_file: str, libparts: str,
                                   start_date: str, end_date: str, region: str,
                                   aoi_result: str, save_dir: str = "pics") -> Tuple[bool, str]:
        """下载指定libParts的所有图片"""
        try:
            # 获取图片地址（精确匹配libParts、地区和AOI检测结果）
            image_addresses = self.get_latest_image_address_for_libparts(data_file, libparts, region, aoi_result)
            
            if not image_addresses:
                self.logger.warning(f"未找到libParts {libparts} 的图片地址")
                return False, ""
            
            # 确保保存目录存在
            os.makedirs(save_dir, exist_ok=True)
            
            # 连接SFTP（根据地区选择服务器）
            if not self.connect_sftp(region):
                return False, ""
            
            downloaded_files = []
            m_jpg_file = ""
            
            try:
                # 下载image1到image6的所有图片
                for image_key, remote_path in image_addresses.items():
                    if image_key.startswith('image') and remote_path:
                        # 生成本地文件名
                        filename = os.path.basename(remote_path)
                        # 新的命名方式：日期_地区_AOI检测结果_libParts_image_key_原始文件名
                        if start_date == end_date:
                            prefix = f"{start_date}_{region}_{aoi_result}_{libparts}_{image_key}_"
                        else:
                            prefix = f"{start_date}_{end_date}_{region}_{aoi_result}_{libparts}_{image_key}_"

                        local_filename = prefix + filename
                        local_path = os.path.join(save_dir, local_filename)
                        
                        # 下载图片
                        if self.download_single_image(remote_path, local_path):
                            downloaded_files.append(local_path)
                            
                            # 检查是否是M.jpg结尾的文件
                            if filename.endswith('M.jpg'):
                                m_jpg_file = local_path
                
                self.logger.info(f"libParts {libparts} 的图片下载完成，共下载 {len(downloaded_files)} 张")
                return True, m_jpg_file
                
            finally:
                self.disconnect_sftp()
                
        except Exception as e:
            self.logger.error(f"下载libParts {libparts} 的图片失败: {e}")
            return False, ""
    
    def download_region_images(self, region_data: List[Dict], start_date: str,
                             end_date: str, data_files: List[str], region_name: str,
                             save_dir: str = "pics") -> Dict[str, str]:
        """下载地区所有代码的图片"""
        try:
            libparts_to_images = {}

            for item in region_data:
                libparts = item.get('libparts', '')
                aoi_result = item.get('aoijudgeresult', '')
                if not libparts:
                    continue

                self.logger.info(f"开始下载libParts {libparts} 的图片")

                # 尝试从所有数据文件中查找图片
                success = False
                m_jpg_file = ""

                for data_file in data_files:
                    if os.path.exists(data_file):
                        success, m_jpg_file = self.download_images_for_libparts(
                            data_file, libparts, start_date, end_date, region_name, aoi_result, save_dir
                        )
                        if success:
                            break

                if success and m_jpg_file:
                    libparts_to_images[libparts] = m_jpg_file
                    self.logger.info(f"libParts {libparts} 的主图片: {m_jpg_file}")
                else:
                    self.logger.warning(f"libParts {libparts} 的图片下载失败")

            return libparts_to_images
            
        except Exception as e:
            self.logger.error(f"下载地区图片失败: {e}")
            return {}
    
    def find_image_for_libparts(self, libparts: str, start_date: str,
                              end_date: str, save_dir: str = "pics") -> str:
        """查找指定libParts的图片文件"""
        try:
            if not os.path.exists(save_dir):
                return ""

            # 查找M.jpg结尾的文件（新命名方式）
            for filename in os.listdir(save_dir):
                # 新命名格式：日期_地区_AOI检测结果_libParts_image_key_原始文件名
                if libparts in filename and filename.endswith('M.jpg'):
                    # 进一步验证是否匹配日期范围
                    if start_date in filename or end_date in filename:
                        return os.path.join(save_dir, filename)

            return ""

        except Exception as e:
            self.logger.error(f"查找libParts {libparts} 的图片失败: {e}")
            return ""

    def find_all_images_for_libparts(self, libparts: str, start_date: str,
                                   end_date: str, region: str = "", aoi_result: str = "",
                                   save_dir: str = "pics") -> Dict[str, str]:
        """查找指定libParts的所有图片文件（image1到image6）"""
        try:
            if not os.path.exists(save_dir):
                return {}

            images = {}

            # 查找image1到image6的所有图片
            for image_key in ['image1', 'image2', 'image3', 'image4', 'image5', 'image6']:
                for filename in os.listdir(save_dir):
                    # 新命名格式：日期_地区_AOI检测结果_libParts_image_key_原始文件名
                    # 精确匹配所有关键字段
                    if self._match_image_filename(filename, libparts, start_date, end_date,
                                                region, aoi_result, image_key):
                        images[image_key] = os.path.join(save_dir, filename)
                        break

            return images

        except Exception as e:
            self.logger.error(f"查找libParts {libparts} 的所有图片失败: {e}")
            return {}

    def _match_image_filename(self, filename: str, libparts: str, start_date: str,
                            end_date: str, region: str, aoi_result: str, image_key: str) -> bool:
        """精确匹配图片文件名"""
        try:
            # 检查基本字段
            if not (libparts in filename and image_key in filename):
                return False

            # 检查日期
            if not (start_date in filename or end_date in filename):
                return False

            # 如果提供了地区和AOI检测结果，则进行精确匹配
            if region and region not in filename:
                return False

            if aoi_result and aoi_result not in filename:
                return False

            # 进一步验证文件名格式：日期_地区_AOI检测结果_libParts_image_key_
            parts = filename.split('_')
            if len(parts) < 6:
                return False

            # 检查libParts和image_key在正确位置
            try:
                libparts_index = -1
                image_key_index = -1

                for i, part in enumerate(parts):
                    if part == libparts:
                        libparts_index = i
                    elif part == image_key:
                        image_key_index = i

                # image_key应该在libParts之后
                if libparts_index >= 0 and image_key_index >= 0 and image_key_index == libparts_index + 1:
                    return True

            except Exception:
                pass

            return False

        except Exception as e:
            self.logger.error(f"匹配文件名失败: {filename}, 错误: {e}")
            return False
