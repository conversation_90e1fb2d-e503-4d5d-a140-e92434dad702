# AOI数据统计分析功能使用说明

## 功能概述

AOI数据下载器现已集成统计分析功能，可以对已下载的Excel数据进行全面的统计分析，包括：

- 基础统计信息（总数据量、AI覆盖率等）
- Top N排名统计（AOI检测结果、物料代码、元件类型、线体）
- 交叉统计分析（物料代码 × AOI检测结果）
- 时间趋势分析

## 界面说明

程序启动后，界面分为两个标签页：

### 1. 数据下载标签页
- 原有的数据下载功能
- 支持分页下载、时间过滤、Excel存储等

### 2. 数据统计标签页
- **统计参数设置区域**：
  - 开始日期：统计的起始日期
  - 结束日期：统计的结束日期
  - Top N：显示前N个结果（5-50可选）
  - 生成统计报告按钮
  - 导出报告按钮（报告生成后可用）

- **统计结果显示区域**：
  - 基础统计：总体数据概览
  - Top统计：各类型的排名统计
  - 交叉统计：物料代码与AOI检测结果的交叉分析

## 使用步骤

### 1. 设置统计参数
1. 选择开始日期和结束日期
   - 默认为最近7天
   - 只会统计该日期范围内的Excel文件
2. 设置Top N数量（默认20）
3. 点击"生成统计报告"

### 2. 查看统计结果

#### 基础统计
显示以下信息：
- 总数据量：统计期间的总记录数
- AI判定数量：AI算法判定的记录数
- AI判定OK：AI判定为OK的记录数
- AI判定NG：AI判定为NG的记录数
- AI算法覆盖：AI算法覆盖的记录数
- AI覆盖率：AI算法覆盖率百分比
- AI判定率：AI判定率百分比

#### Top统计
以表格形式显示：
- AOI检测结果排名（翘件、缺件、错件等）
- 物料代码排名
- 元件类型排名（CHIP-C、ALC等）
- 线体排名

每项包含：名称、数量、占比

#### 交叉统计
显示物料代码与AOI检测结果的交叉分析，帮助识别：
- 哪些物料代码最容易出现特定的AOI检测问题
- 特定AOI问题主要集中在哪些物料上

## 数据字段说明

统计分析基于以下Excel字段：

| 字段名 | 说明 | 用途 |
|--------|------|------|
| aoiJudgeResult | AOI检测结果 | 主要统计维度（翘件、缺件、错件等） |
| libname | 元件类型 | 元件分类统计（CHIP-C、ALC等） |
| libParts | 物料代码 | 物料分析 |
| componentName | 检测位号 | 计数统计 |
| aiAlgorithmName | AI算法名称 | AI覆盖分析 |
| aiJudgeResult | AI判定结果 | AI判定统计（OK/NG/None） |
| lineName | 线体 | 线体分析 |
| createTime | 创建时间 | 时间范围过滤和趋势分析 |

## 数据过滤规则

为确保统计准确性，系统会自动过滤：

1. **测试线体数据**：过滤线体名称为"SMT-NJ777"的数据
2. **测试检测结果**：过滤以下测试数据
   - 极性相反 · 测试、极性相反 · 测
   - 缺件 · 测试、缺件 · 测
   - 错件 · 测试、错件 · 测
   - 偏移 · 测试、偏移 · 测

## 注意事项

1. **数据来源**：统计分析基于xlsxs文件夹中的Excel文件
2. **文件命名**：Excel文件必须按照`aoi_data_YYYY-MM-DD.xlsx`格式命名
3. **日期范围**：只统计指定日期范围内的文件，如果某天没有对应文件则跳过
4. **性能考虑**：大量数据的统计可能需要较长时间，请耐心等待
5. **内存使用**：统计大量数据时会占用较多内存

## 常见问题

### Q: 为什么统计结果为空？
A: 可能原因：
- 指定日期范围内没有Excel文件
- Excel文件中没有有效数据（全部被过滤）
- 文件命名格式不正确

### Q: 统计速度很慢怎么办？
A: 建议：
- 缩小统计的日期范围
- 减少Top N的数量
- 确保有足够的内存空间

### Q: 如何导出统计报告？
A: 统计完成后，"导出报告"按钮会变为可用状态（功能待实现）

## 技术实现

- **统计引擎**：基于pandas进行高效数据分析
- **多线程处理**：统计任务在后台线程执行，不阻塞界面
- **内存优化**：按需加载数据，避免内存溢出
- **异常处理**：完善的错误处理和日志记录

## 更新日志

- v1.0：基础统计分析功能
- 支持基础统计、Top排名、交叉分析
- 集成到主界面的标签页中
- 支持自定义统计参数
