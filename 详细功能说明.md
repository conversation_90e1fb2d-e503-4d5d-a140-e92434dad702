# AOI数据下载与统计分析系统 - 详细功能说明

## 1. 生成统计报告按钮详细功能

### 1.1 功能概述
当用户点击"生成统计报告"按钮后，系统将启动完整的统计分析流程，生成包含多维度分析结果的综合报告。

### 1.2 执行流程
1. **参数验证**
   - 验证开始日期和结束日期的有效性
   - 检查结束日期不能早于开始日期
   - 验证TOP N数值的合理性

2. **数据加载**
   - 根据指定的时间范围，从`data/`目录中查找对应的Excel文件
   - 支持CSV和XLSX格式的数据文件
   - 按日期顺序合并所有匹配的数据文件

3. **数据清洗**
   - 过滤模式为"生产"的数据
   - 排除测试线体"SMT-NJ777"的数据
   - 过滤测试性质的AOI检测结果
   - 处理缺失值和异常数据

4. **地区分类**
   - 按线体名称前缀进行地区分类：
     - 南京地区：以"SMT-NJ"开头的线体
     - 河源地区：以"SMT-HY"开头的线体
     - 其他地区：其余所有线体

5. **多维度统计分析**
   - **基础统计**：计算总数据量、AI覆盖数、AI判定率等关键指标
   - **AOI缺陷统计**：按各类缺陷类型统计数量和占比
   - **物料代码统计**：统计各物料代码的数量分布
   - **元件类型统计**：分析不同元件类型的分布情况
   - **线体统计**：统计各线体的数据量分布
   - **交叉统计**：分析物料代码与AOI结果的关联关系
   - **时间趋势**：分析数据随时间的变化趋势

6. **报告生成**
   - 将统计结果组织成结构化的数据格式
   - 为每个地区生成独立的统计报告
   - 计算各项统计指标的百分比和比率
   - 生成时间范围和报告生成时间戳

7. **界面更新**
   - 在对应的地区标签页中显示统计结果
   - 更新统计结果表格和文本显示
   - 激活导出相关按钮（导出XLSX报告、导出HTML报告、下载南京图片）

### 1.3 输出结果
生成的报告包含以下内容：
- 各地区的基础统计信息
- 各类AOI缺陷的TOP N排名
- 物料代码的TOP N排名
- 元件类型的TOP N排名
- 线体的TOP N排名
- 交叉统计分析结果
- 时间趋势分析图表

## 2. 下载南京图片按钮详细功能

### 2.1 功能概述
该按钮用于批量下载南京地区物料代码对应的AOI检测图片，方便用户进行人工审核和分析。

### 2.2 执行流程
1. **前置检查**
   - 验证是否存在有效的统计报告数据
   - 检查统计报告中是否包含南京地区的数据
   - 确认系统配置中的SFTP连接信息

2. **数据准备**
   - 从统计报告中提取南京地区代码TOP N统计中的物料代码列表
   - 获取统计的时间范围信息
   - 准备下载所需的参数信息

3. **SFTP连接**
   - 建立到SFTP服务器（***********:2222）的安全连接
   - 使用配置文件中的用户名和密码进行身份认证
   - 确保连接稳定性和安全性

4. **图片地址解析**
   - 从AOI数据文件中查找对应物料代码的图片地址信息
   - 解析imageResultAddress字段中的JSON格式数据
   - 提取所有相关的图片文件路径

5. **批量下载**
   - 遍历所有需要下载的图片地址
   - 从SFTP服务器下载每一张图片文件
   - 为每张图片生成规范的本地文件名
   - 保存到`pics/`目录中

6. **文件命名规则**
   - 文件名格式：`开始日期_结束日期_物料代码_原始文件名`
   - 例如：`2025-08-01_2025-08-07_BGA-c0060213I0073_20250807.jpg`
   - 主图片文件（M.jpg结尾）会被特别标识

7. **完成处理**
   - 记录下载成功的图片数量
   - 提供下载进度和状态信息
   - 在界面中显示下载完成提示

### 2.3 注意事项
- 需要稳定的网络连接和SFTP服务器访问权限
- 下载的图片文件会保存在`pics/`目录中
- 系统会自动识别并下载每个物料代码的主图片文件

## 3. 导出XLSX报告按钮详细功能

### 3.1 功能概述
将生成的统计报告导出为Excel格式文件，便于用户进行进一步的数据分析和分享。

### 3.2 执行流程
1. **文件准备**
   - 检查是否存在有效的统计报告数据
   - 确定导出文件的名称（包含时间范围信息）
   - 验证导出目录是否存在

2. **数据结构化**
   - 将统计报告数据转换为适合Excel格式的结构
   - 按照预定义的列顺序组织数据
   - 确保数据类型正确（数字、文本、日期等）

3. **Excel文件创建**
   - 创建新的Excel工作簿
   - 为每个地区创建独立的工作表
   - 按照指定的格式和样式设置表格

4. **工作表内容填充**
   - **总览统计表**：包含各地区的基础统计信息
   - **缺陷统计表**：展示各类AOI缺陷的详细统计
   - **代码统计表**：显示物料代码的TOP N统计结果
   - **交叉统计表**：展示物料代码与AOI结果的交叉分析

5. **格式设置**
   - 设置合适的列宽和行高
   - 应用适当的字体和颜色样式
   - 添加边框和背景色区分不同区域
   - 确保数字格式正确显示

6. **文件保存**
   - 将Excel文件保存到`exports/`目录
   - 文件名格式：`AOI统计报告_开始日期_结束日期.xlsx`
   - 如果开始日期和结束日期相同，则为：`AOI统计报告_日期.xlsx`

7. **完成提示**
   - 显示导出成功的提示信息
   - 提供文件保存路径信息
   - 在日志中记录导出操作

### 3.3 Excel文件结构
导出的Excel文件包含以下工作表：
- `[地区名]_总览`：基础统计信息
- `[地区名]_缺陷统计`：各类缺陷统计
- `[地区名]_代码统计`：物料代码统计
- `[地区名]_TOPN处理情况`：南京和河源地区的TOP N处理情况表格

## 4. 导出HTML报告按钮详细功能

### 4.1 功能概述
将生成的统计报告导出为HTML格式文件，提供美观的网页展示效果。

### 4.2 执行流程
1. **文件准备**
   - 验证统计报告数据的有效性
   - 生成HTML文件的名称（包含时间范围信息）
   - 确保导出目录存在

2. **HTML内容生成**
   - 创建HTML文档的基本结构
   - 添加CSS样式表定义
   - 构建报告标题和时间范围信息

3. **内容组织**
   - **头部信息**：报告标题、统计时间范围、生成时间
   - **TOP N处理情况表格**：南京和河源地区的TOP N处理情况
   - **各地区详细统计**：
     - 总览统计信息
     - 各类缺陷统计表格
     - 代码TOP N统计表格

4. **样式设计**
   - 使用CSS样式美化报告外观
   - 为不同区域设置不同的背景色
   - 添加表格边框和间距
   - 优化文字排版和可读性

5. **特殊处理**
   - 为南京和河源地区添加专门的TOP N处理情况表格
   - 在表格中包含图片链接（如果有下载的图片）
   - 为重要数据添加突出显示效果

6. **文件保存**
   - 将生成的HTML内容保存到`exports/`目录
   - 文件名格式：`AOI统计报告_开始日期_结束日期.html`
   - 如果开始日期和结束日期相同，则为：`AOI统计报告_日期.html`

7. **完成提示**
   - 显示导出成功的提示信息
   - 提供文件保存路径信息
   - 在日志中记录导出操作

### 4.3 HTML文件特点
- 响应式设计，适配不同屏幕尺寸
- 包含完整的统计信息和表格
- 使用CSS样式美化报告外观
- 支持在浏览器中直接打开查看
- 包含南京和河源地区的TOP N处理情况表格

## 5. 系统集成与用户体验

### 5.1 按钮状态管理
- 生成统计报告按钮：初始状态可用
- 下载南京图片按钮：初始状态禁用，统计完成后启用
- 导出XLSX报告按钮：初始状态禁用，统计完成后启用
- 导出HTML报告按钮：初始状态禁用，统计完成后启用

### 5.2 用户反馈机制
- 操作过程中实时显示状态信息
- 操作完成后提供明确的成功/失败提示
- 日志系统记录所有关键操作
- 提供进度条和详细日志信息

### 5.3 错误处理
- 对各种异常情况进行妥善处理
- 提供友好的错误提示信息
- 记录详细的错误日志
- 确保系统稳定性，防止因个别操作失败影响整体运行
